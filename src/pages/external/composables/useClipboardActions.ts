import { copyToClipboard, useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import type { OrderData } from './useOrderData';

export function useClipboardActions(orderData: () => OrderData) {
  const q = useQuasar();
  const { t } = useI18n();

  const copyToClipboardWithNotification = (
    text: string,
    successMessage: string
  ) => {
    copyToClipboard(text)
      .then(() => {
        q.notify({
          color: 'positive',
          message: t(successMessage),
          icon: 'content_copy',
        });
      })
      .catch(() => {
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.copyFailed'),
          icon: 'error',
        });
      });
  };

  const copyRecipientBankName = () => {
    copyToClipboardWithNotification(
      orderData().recipientBank,
      'externalPaymentDetailPage.copiedRecipientBankName'
    );
  };

  const copyRecipientBranchCode = () => {
    copyToClipboardWithNotification(
      orderData().recipientBranchCode,
      'externalPaymentDetailPage.copyBranchCode'
    );
  };

  const copyRecipientName = () => {
    copyToClipboardWithNotification(
      orderData().recipientName,
      'externalPaymentDetailPage.copiedRecipientName'
    );
  };

  const copyRecipientAccount = () => {
    copyToClipboardWithNotification(
      orderData().recipientAccount,
      'externalPaymentDetailPage.copiedAccountNumber'
    );
  };

  const copyPayerBankAccountName = () => {
    copyToClipboardWithNotification(
      orderData().payerBankAccountName,
      'externalPaymentDetailPage.copiedPayerBankAccountName'
    );
  };

  const copyFiatAmount = () => {
    copyToClipboardWithNotification(
      orderData().paymentAmount.replace(orderData().fiatCurrency, ''),
      'externalPaymentDetailPage.copiedFiatAmount'
    );
  };

  const copyOrderNumber = (orderNumber: string, messageKey: string) => {
    copyToClipboardWithNotification(orderNumber, messageKey);
  };

  return {
    copyRecipientBankName,
    copyRecipientBranchCode,
    copyRecipientName,
    copyRecipientAccount,
    copyPayerBankAccountName,
    copyFiatAmount,
    copyOrderNumber,
  };
}
