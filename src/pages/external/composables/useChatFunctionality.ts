import { ref, nextTick, watch, type Ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useChatStore } from 'src/stores';
import { useGetMessages, useSendChatImage } from '../api';
import { SenderRole } from 'src/utils/enums';
import type { OrderData } from './useOrderData';
import messageSoundFile from 'src/assets/sounds/message.mp3';

export interface ChatMessage {
  content: string;
  time: string;
  senderRole: SenderRole;
  isImage: boolean;
  caption?: string | null;
}

export function useChatFunctionality(
  orderData: () => OrderData,
  isConnected: Ref<boolean>
) {
  const q = useQuasar();
  const { t } = useI18n();
  const chatStore = useChatStore();

  const chatMessages = ref<ChatMessage[]>([]);
  const messageInput = ref<string>('');
  const chatMessagesRef = ref<HTMLElement | null>(null);
  const fileInput = ref<HTMLInputElement | null>(null);

  // API hooks
  const { data: apiMessages, run: fetchMessages } = useGetMessages({});
  const { run: sendChatImage } = useSendChatImage({});

  const scrollToBottom = () => {
    nextTick(() => {
      if (chatMessagesRef.value) {
        const element = chatMessagesRef.value as HTMLDivElement;
        element.scrollTop = element.scrollHeight;
      }
    });
  };

  const playMessageSound = () => {
    try {
      const audio = new Audio(messageSoundFile);
      audio.volume = 0.5; // Set volume to 50%
      audio.play().catch((error) => {
        console.log('Could not play message sound:', error);
      });
    } catch (error) {
      console.log('Error creating audio:', error);
    }
  };

  const openImagePreview = (imageUrl: string) => {
    q.dialog({
      title: t('externalPaymentDetailPage.imagePreview'),
      message: `
        <div class="q-pa-md text-center">
          <img
            src="${imageUrl}"
            style="max-width: 100%; max-height: 70vh; border-radius: 8px; object-fit: contain;"
            alt="Preview"
          />
        </div>
      `,
      html: true,
      style: 'max-width: 90vw; width: 100%;',
      ok: {
        label: t('externalPaymentDetailPage.closeButton'),
        color: 'primary',
      },
    });
  };

  const openFileDialog = () => {
    nextTick(() => {
      if (fileInput.value) {
        fileInput.value.click();
      } else {
        console.error('File input element not found');
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.imageUploadError'),
          icon: 'error',
        });
      }
    });
  };

  const handleFileSelected = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (!target.files || !target.files.length) return;

    const file = target.files[0];

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.invalidFileType'),
        icon: 'error',
      });
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.fileTooLarge'),
        icon: 'error',
      });
      return;
    }

    try {
      // Use the API to send the image
      sendChatImage({
        tradeId: orderData().chatRoomId,
        file: file,
      });

      // Play sound for outgoing image
      playMessageSound();

      // Reset file input
      if (fileInput.value) {
        fileInput.value.value = '';
      }

      // The new message will be received via SignalR and added to the chat
    } catch (error) {
      console.error('Error sending image:', error);
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.imageUploadError'),
        icon: 'error',
      });
    }
  };

  const sendMessage = async () => {
    if (!messageInput.value.trim()) return;

    try {
      // Use the sendChatMessage function from useClientHub
      if (isConnected.value) {
        await chatStore.sendMessage({
          tradeId: orderData().chatRoomId,
          payload: messageInput.value,
          messageType: 0, // Text message
        });

        // Play sound for outgoing message
        playMessageSound();

        // Clear the input field
        messageInput.value = '';

        // The message will be received via SignalR and added to the chat
      } else {
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.connectionError'),
          icon: 'error',
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.messageSendError'),
        icon: 'error',
      });
    }
  };

  const getMessageClass = (senderRole: SenderRole) => {
    switch (senderRole) {
      case SenderRole.SupportTeam:
        return 'support-team-message';
      case SenderRole.Agent:
        return 'agent-message';
      case SenderRole.User:
        return 'user-message';
      default:
        return 'user-message';
    }
  };

  // Transform API messages to our chat format
  watch(
    () => apiMessages.value,
    (newMessages) => {
      if (newMessages && newMessages.length > 0) {
        chatMessages.value = newMessages.map((msg) => ({
          content: msg.messageType === 1 ? msg.payload : msg.payload, // Image or text
          time: new Date(msg.sentAt).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          senderRole: msg.senderRole,
          isImage: msg.messageType === 1, // 1 is image type
          caption: msg.caption,
        }));
        scrollToBottom();
      }
    }
  );

  // Watch for new messages from SignalR
  watch(
    () => chatStore.message,
    (newMessage) => {
      if (newMessage && orderData().chatRoomId === newMessage.tradeId) {
        chatMessages.value.push({
          content:
            newMessage.messageType === 1
              ? newMessage.payload
              : newMessage.payload,
          time: new Date(newMessage.sentAt).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          senderRole: newMessage.senderRole,
          isImage: newMessage.messageType === 1,
        });

        // Play sound for new messages from CS/Agent
        if (newMessage.senderRole !== SenderRole.User) {
          playMessageSound();
        }

        scrollToBottom();
      }
    }
  );

  return {
    chatMessages,
    messageInput,
    chatMessagesRef,
    fileInput,
    fetchMessages,
    scrollToBottom,
    openImagePreview,
    openFileDialog,
    handleFileSelected,
    sendMessage,
    getMessageClass,
  };
}
