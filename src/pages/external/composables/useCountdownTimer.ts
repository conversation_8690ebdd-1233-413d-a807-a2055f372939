import { ref, nextTick, type Ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import type { OrderData } from './useOrderData';

export function useCountdownTimer(
  tradeStatus: Ref<string>,
  orderData: () => OrderData
) {
  const q = useQuasar();
  const { t } = useI18n();

  const countdownRef = ref(null);
  const countdownTime = ref(0); // In milliseconds

  const startCountdown = (
    state:
      | 'WAITING_FOR_AGENT_RESPONSE'
      | 'WAITING_FOR_CONFIRMATION'
      | 'WAITING_FOR_AGENT_CONFIRMATION' = 'WAITING_FOR_CONFIRMATION'
  ) => {
    // Don't start countdown if we don't have order data yet
    if (!orderData()?.updatedAt) {
      countdownTime.value = 5 * 60 * 1000; // Hide countdown until we have real data
      return;
    }

    const updatedAtTime = new Date(orderData().updatedAt).getTime();
    const now = Date.now();
    const elapsedSeconds = Math.floor((now - updatedAtTime) / 1000);

    let durationSeconds = 0;
    if (state === 'WAITING_FOR_CONFIRMATION') {
      durationSeconds = 30 * 60; // 30 minutes
    } else if (
      state === 'WAITING_FOR_AGENT_RESPONSE' ||
      state === 'WAITING_FOR_AGENT_CONFIRMATION'
    ) {
      durationSeconds = 5 * 60; // 5 minutes
    }

    const remaining = Math.max(durationSeconds - elapsedSeconds, 0);
    countdownTime.value = remaining * 1000;

    // Restart countdown
    nextTick(() => {
      (countdownRef.value as any)?.start();
    });
  };

  const handleCountdownEnd = () => {
    if (orderData().orderStatus === 'Pending') {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.orderExpired'),
        icon: 'timer_off',
      });
      tradeStatus.value = 'Expired';
    }
  };

  return {
    countdownRef,
    countdownTime,
    startCountdown,
    handleCountdownEnd,
  };
}
