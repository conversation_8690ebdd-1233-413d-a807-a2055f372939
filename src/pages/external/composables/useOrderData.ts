import { reactive, watchEffect, type Ref } from 'vue';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { TradeTypeEnum } from 'src/utils/enums';
import { ProxyOrder } from '../api';

export interface OrderData {
  exchangeRate: string;
  orderTime: string;
  paymentAmount: string;
  orderNumber: string;
  recipientBank: string;
  recipientAccount: string;
  recipientName: string;
  recipientBranchCode: string;
  paymentMethod: string;
  payerBankAccountName: string;
  updatedAt: string;
  chatRoomId: number;
  cryptoAmount: string;
  cryptoSymbol: string;
  agentFee: string;
  orderStatus: string;
  disputeStatus: string;
  network: string;
  tradeTypeDesc: string;
  tradeType: TradeTypeEnum;
  completedAt: string | null;
  fiatAmount: number;
  fiatCurrency: string;
}
export function useOrderData(proxyOrder: Ref<ProxyOrder | undefined>) {
  const orderData = reactive<OrderData>({
    exchangeRate: '',
    orderTime: '',
    paymentAmount: '',
    orderNumber: '',
    recipientBank: '',
    recipientAccount: '',
    recipientName: '',
    recipientBranchCode: '',
    paymentMethod: '',
    payerBankAccountName: '',
    updatedAt: '',
    chatRoomId: 0,
    cryptoAmount: '',
    cryptoSymbol: '',
    agentFee: '',
    orderStatus: '',
    disputeStatus: 'None',
    network: '',
    tradeTypeDesc: '',
    tradeType: TradeTypeEnum.Buy,
    completedAt: '',
    fiatAmount: 0,
    fiatCurrency: '',
  });

  watchEffect(() => {
    const order = proxyOrder.value;
    if (!order) return;

    orderData.exchangeRate = `1 ${order.cryptoSymbol} = ${order.rate} ${order.fiatCurrency}`;
    orderData.orderTime = new Date(order.createdAt).toLocaleString();
    orderData.paymentAmount = `${order.fiatCurrency} ${thousandTool(
      order.fiatAmount,
      order.fiatCurrency as DigitTypes
    )}`;
    orderData.orderNumber = order.tradeNo;
    orderData.recipientBank = order.bankName || '';
    orderData.recipientAccount = order.accountNumber || '';
    orderData.recipientName = order.accountName || '';
    orderData.recipientBranchCode = order.branchCode || '';
    orderData.paymentMethod = order.bankName || '';
    orderData.payerBankAccountName = order.payerBankAccountName || '';
    orderData.updatedAt = order.updatedAt;
    orderData.chatRoomId = order.id;
    orderData.cryptoAmount = `${thousandTool(
      order.cryptoAmount,
      order.cryptoSymbol as DigitTypes
    )} ${order.cryptoSymbol}`;
    orderData.cryptoSymbol = order.cryptoSymbol;
    orderData.agentFee = `${order.fiatCurrency} ${thousandTool(
      order.agentFeeAmount,
      order.fiatCurrency as DigitTypes
    )}`;
    orderData.orderStatus = order.statusDesc;
    orderData.disputeStatus = order.disputeStatusDesc;
    orderData.network = order.networkDesc;
    orderData.tradeTypeDesc = order.typeDesc;
    orderData.tradeType = order.type;
    orderData.completedAt = order.completedAt;
    orderData.fiatAmount = order.fiatAmount;
    orderData.fiatCurrency = order.fiatCurrency;
  });

  return { orderData };
}
