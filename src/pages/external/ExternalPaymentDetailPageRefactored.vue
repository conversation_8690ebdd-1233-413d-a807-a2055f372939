<template>
  <q-page
    padding
    class="container q-py-lg flex items-center column justify-center"
  >
    <!-- Waiting Overlay -->
    <WaitingOverlay
      :trade-status="tradeStatus"
      :countdown-time="countdownTime"
      :countdown-ref="countdownRef"
      :handle-countdown-end="handleCountdownEnd"
      :verifying="verifying"
    />

    <!-- Loading/Error States -->
    <LoadingErrorStates
      v-if="
        verifying ||
        agentNotAcceptingOrderError ||
        orderExpiredError ||
        verificationError
      "
      :verifying="verifying"
      :agent-not-accepting-order-error="agentNotAcceptingOrderError"
      :order-expired-error="orderExpiredError"
      :verification-error="verificationError"
      :order-number="orderData.orderNumber"
      @retry="verifyEntryCodeFromUrl"
    />

    <!-- Payment details -->
    <q-card
      v-else
      :class="[
        'wrapper',
        q.screen.lt.sm ? 'q-pa-md' : 'q-pa-lg',
        themeStore.isDark ? 'bg-dark' : 'bg-white',
      ]"
      style="max-width: 1200px"
    >
      <div class="text-center text-h5 q-mb-lg">
        {{ t('externalPaymentDetailPage.title') }}
      </div>

      <!-- Countdown timer -->
      <div
        v-if="!shouldNotCountdown && tradeStatus !== 'Completed'"
        class="q-mb-lg"
      >
        <vue-countdown
          ref="countdownRef"
          :time="countdownTime"
          @end="handleCountdownEnd"
          v-slot="{ minutes, seconds, totalMilliseconds }"
        >
          <div class="countdown-container q-mb-lg">
            <div class="countdown-label">
              {{ t('externalPaymentDetailPage.timeRemaining') }}
            </div>
            <div class="countdown-timer">
              {{ String(minutes).padStart(2, '0') }}:{{
                String(seconds).padStart(2, '0')
              }}
            </div>
            <q-linear-progress
              :value="totalMilliseconds / countdownTime"
              color="warning"
              size="10px"
            />
          </div>
        </vue-countdown>
      </div>

      <div class="row q-col-gutter-md">
        <!-- Left side: Order details -->
        <div class="col-12 col-md-7">
          <!-- Active Order Details -->
          <div
            v-if="tradeStatus !== 'Completed' && tradeStatus !== 'Cancelled'"
            :class="[
              themeStore.isDark ? 'bg-dark' : 'bg-white',
              'modern-order-container q-mx-auto',
            ]"
            style="max-width: 700px"
          >
            <OrderDetailsCard
              :order-data="orderData"
              @copy-order-number="copyOrderNumber"
              @copy-fiat-amount="copyFiatAmount"
              @copy-recipient-bank-name="copyRecipientBankName"
              @copy-recipient-account="copyRecipientAccount"
              @copy-recipient-branch-code="copyRecipientBranchCode"
              @copy-recipient-name="copyRecipientName"
              @copy-payer-bank-account-name="copyPayerBankAccountName"
            />

            <!-- Payment Notice -->
            <PaymentNotice
              :trade-status="tradeStatus"
              :trade-type="orderData.tradeType"
            />

            <!-- Action Buttons -->
            <ActionButtons
              ref="actionButtonsRef"
              :trade-status="tradeStatus"
              :confirmed-paid="confirmedPaid"
              :confirmed-proof="confirmedProof"
              :loading="loading"
              :uploading-payment-proof="uploadingPaymentProof"
              :payment-proof-url="paymentProofUrl?.url || null"
              :trade-type="orderData.tradeType"
              :dispute-loading="disputeLoading"
              :dispute-status="orderData.disputeStatus"
              @confirm-payment="handleConfirmPayment"
              @show-payment-proof="showPaymentProof"
              @open-payment-proof-dialog="openPaymentProofDialog"
              @payment-proof-selected="handlePaymentProofSelected"
              @open-dispute="handleDispute"
            />
          </div>

          <!-- Cancelled Order Card -->
          <CancelledOrderCard
            v-else-if="tradeStatus === 'Cancelled'"
            :order-data="orderData"
            @copy-order-number="copyOrderNumber"
          />

          <!-- Completed Order Card -->
          <CompletedOrderCard
            v-else
            :order-data="orderData"
            @copy-order-number="copyOrderNumber"
            @copy-account-number="copyToClipboardWithNotification"
          />
        </div>

        <!-- Right side: Chat -->
        <div class="col-12 col-md-5">
          <ChatInterface
            ref="chatInterfaceRef"
            :trade-status="tradeStatus"
            :chat-messages="chatMessages"
            v-model:message-input="messageInput"
            :get-message-class="getMessageClass"
            @send-message="sendMessage"
            @open-file-dialog="openFileDialog"
            @file-selected="handleFileSelected"
            @open-image-preview="openImagePreview"
            @scroll-to-bottom="scrollToBottom"
          />
        </div>
      </div>

      <!-- Notes section -->
      <NotesSection />
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { useRoute } from 'vue-router';
import {
  useAuthStore,
  useChatStore,
  useThemeStore,
  useTradeStore,
} from 'src/stores';
import { useGetMessages, useGetProxyOrder, useVerifyEntryCode } from './api';
import { TradeTypeEnum } from 'src/utils/enums';
import { useClientHub } from 'src/hooks/useClientHub';
import { TradeStatusEnum } from 'src/utils/enums';
import VueCountdown from '@chenfengyuan/vue-countdown';

// Import components
import WaitingOverlay from './components/WaitingOverlay.vue';
import LoadingErrorStates from './components/LoadingErrorStates.vue';
import OrderDetailsCard from './components/OrderDetailsCard.vue';
import PaymentNotice from './components/PaymentNotice.vue';
import ActionButtons from './components/ActionButtons.vue';
import CancelledOrderCard from './components/CancelledOrderCard.vue';
import CompletedOrderCard from './components/CompletedOrderCard.vue';
import ChatInterface from './components/ChatInterface.vue';
import NotesSection from './components/NotesSection.vue';

// Import composables
import { useOrderData } from './composables/useOrderData';
import { useClipboardActions } from './composables/useClipboardActions';
import { useCountdownTimer } from './composables/useCountdownTimer';
import { usePaymentActions } from './composables/usePaymentActions';
import { useChatFunctionality } from './composables/useChatFunctionality';
import { DigitTypes, thousandTool } from '../../utils/NumberTool';
import { storageHelper } from '../../utils/foragePkg';

const { t, locale } = useI18n({ useScope: 'global' });

// Set Chinese as default language for this external page if no language is stored
const storedLocale = storageHelper('locale').getItem();
if (!storedLocale) {
  locale.value = 'zh-CN';
  storageHelper('locale').setItem('zh-CN');
}

const q = useQuasar();
const route = useRoute();
const themeStore = useThemeStore();
const authStore = useAuthStore();
const tradeStore = useTradeStore();
const chatStore = useChatStore();

// Reactive state
const verifying = ref<boolean>(true);
const verificationError = ref<boolean>(false);
const agentNotAcceptingOrderError = ref<boolean>(false);
const orderExpiredError = ref<boolean>(false);
const entryCode = ref<string>('');
const tradeStatus = ref<string>('Pending'); // Initialize to 'Pending' to show overlay immediately
const confirmedPaid = ref<boolean>(false);
const confirmedProof = ref<boolean>(false);

// Component refs
const actionButtonsRef = ref();
const chatInterfaceRef = ref();

// API hooks
const { run: verifyEntryCode, error: verifyEntryCodeError } =
  useVerifyEntryCode({
    onSuccess: () => {
      fetchProxyOrder({});
      verifying.value = false;
      verificationError.value = false;
    },
    onError: (error) => {
      console.error('Error verifying entry code:', error);
      verifying.value = false;
      verificationError.value = true;
    },
  });

const { data: proxyOrder, run: fetchProxyOrder } = useGetProxyOrder();

// SignalR
const { isConnected, startConnection } = useClientHub(true);

// Composables
const { orderData } = useOrderData(proxyOrder);
const { countdownRef, countdownTime, startCountdown, handleCountdownEnd } =
  useCountdownTimer(tradeStatus, () => orderData);
const {
  copyRecipientBankName,
  copyRecipientBranchCode,
  copyRecipientName,
  copyRecipientAccount,
  copyPayerBankAccountName,
  copyFiatAmount,
  copyOrderNumber,
} = useClipboardActions(() => orderData);
const tradeTypeRef = computed(() => proxyOrder.value?.type);
const {
  loading,
  uploadingPaymentProof,
  paymentProofUrl,
  disputeLoading,
  handleConfirmPayment,
  handlePaymentProofSelected,
  showPaymentProof,
  handleDispute,
} = usePaymentActions(
  entryCode,
  () => orderData,
  confirmedPaid,
  confirmedProof,
  tradeTypeRef
);
const {
  chatMessages,
  messageInput,
  fetchMessages,
  scrollToBottom,
  openImagePreview,
  openFileDialog,
  handleFileSelected,
  sendMessage,
  getMessageClass,
} = useChatFunctionality(() => orderData, isConnected);

// Computed
const shouldNotCountdown = computed(() => {
  return (
    orderData.orderStatus === 'Completed' ||
    orderData.orderStatus === 'Cancelled' ||
    orderData.orderStatus === 'Expired'
  );
});

// Helper function for clipboard with notification
const copyToClipboardWithNotification = (text: string, messageKey: string) => {
  copyOrderNumber(text, messageKey);
};

// Function to verify entry code from URL
const verifyEntryCodeFromUrl = async () => {
  verifying.value = true;
  verificationError.value = false;

  const { entryCode: routeEntryCode } = route.params;
  if (routeEntryCode && typeof routeEntryCode === 'string') {
    entryCode.value = routeEntryCode;
    verifyEntryCode({
      entryCode: entryCode.value,
    });
  } else {
    console.error('No entry code provided');
    verifying.value = false;
    verificationError.value = true;
  }
};

const openPaymentProofDialog = () => {
  nextTick(() => {
    if (actionButtonsRef.value?.paymentProofInput) {
      actionButtonsRef.value.paymentProofInput.click();
    } else {
      console.error('Payment proof input element not found');
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.imageUploadError'),
        icon: 'error',
      });
    }
  });
};

// Watchers
watch(
  () => proxyOrder.value,
  async (newOrder) => {
    if (newOrder) tradeStatus.value = newOrder.statusDesc;
    if (newOrder && !verifyEntryCodeError.value) {
      // Fix: Handle confirmedPaid logic differently for buy vs sell orders
      if (newOrder.type === TradeTypeEnum.Sell) {
        // For sell orders: user confirms payment received only after PaymentConfirmed status
        confirmedPaid.value =
          newOrder.status === TradeStatusEnum.PaymentConfirmed ||
          newOrder.status === TradeStatusEnum.Completed ||
          newOrder.status === TradeStatusEnum.Cancelled ||
          newOrder.status === TradeStatusEnum.Expired;
      } else {
        // For buy orders: user confirms payment made at PaymentMade status
        confirmedPaid.value =
          newOrder.status === TradeStatusEnum.PaymentMade ||
          newOrder.status === TradeStatusEnum.PaymentConfirmed ||
          newOrder.status === TradeStatusEnum.Completed ||
          newOrder.status === TradeStatusEnum.Cancelled ||
          newOrder.status === TradeStatusEnum.Expired;
      }

      confirmedProof.value =
        newOrder.status === TradeStatusEnum.Completed ||
        newOrder.status === TradeStatusEnum.Cancelled ||
        newOrder.status === TradeStatusEnum.Expired;
      fetchMessages({ tradeId: newOrder.id });

      if (authStore.proxyToken) await startConnection(authStore.proxyToken);
      scrollToBottom();
    }
  }
);

watch(
  () => tradeStore.trade,
  (newTrade) => {
    if (newTrade && orderData.chatRoomId === newTrade.id) {
      tradeStore.playProgressSound();
      tradeStatus.value = newTrade.statusDesc;
      orderData.orderStatus = newTrade.statusDesc;
      orderData.updatedAt = newTrade.updatedAt;
      orderData.disputeStatus = newTrade.disputeStatusDesc;
      orderData.network = newTrade.networkDesc;
      orderData.tradeTypeDesc = newTrade.typeDesc;
      orderData.tradeType = newTrade.type;
      orderData.cryptoAmount = `${thousandTool(
        newTrade.cryptoAmount,
        newTrade.cryptoSymbol as unknown as DigitTypes
      )} ${newTrade.cryptoSymbol}`;
      orderData.agentFee = `${newTrade.fiatCurrency} ${thousandTool(
        newTrade.agentFeeAmount,
        newTrade.fiatCurrency as unknown as DigitTypes
      )}`;
      orderData.paymentAmount = `${newTrade.fiatCurrency} ${thousandTool(
        newTrade.fiatAmount,
        newTrade.fiatCurrency as unknown as DigitTypes
      )}`;
      orderData.payerBankAccountName = newTrade.payerBankAccountName;
      orderData.exchangeRate = `1 ${newTrade.cryptoSymbol} = ${proxyOrder.value?.rate} ${proxyOrder.value?.fiatCurrency}`;
      orderData.orderTime = new Date(newTrade.createdAt).toLocaleString();
      orderData.updatedAt = newTrade.updatedAt;
      orderData.fiatAmount = newTrade.fiatAmount;
      orderData.orderNumber = newTrade.tradeNo;
      orderData.cryptoSymbol = newTrade.cryptoSymbol;
      orderData.fiatCurrency = newTrade.fiatCurrency;
      orderData.paymentMethod = newTrade.bankName;
      orderData.completedAt = newTrade.completedAt;
      orderData.recipientBank = newTrade.bankName;
      orderData.recipientAccount = newTrade.accountNumber;
      orderData.recipientName = newTrade.accountName;
      orderData.recipientBranchCode = newTrade.branchCode;
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => tradeStatus.value,
  () => {
    if (tradeStatus.value === 'BankAccountAssigned') {
      startCountdown('WAITING_FOR_CONFIRMATION');
    } else if (tradeStatus.value === 'Pending') {
      startCountdown('WAITING_FOR_AGENT_RESPONSE');
    } else if (tradeStatus.value === 'PaymentMade') {
      startCountdown('WAITING_FOR_AGENT_CONFIRMATION');
    }
    if (tradeStatus.value === 'Expired') {
      confirmedPaid.value = true;
      confirmedProof.value = true;
      orderExpiredError.value = true;
    }
  },
  { immediate: true }
);

// Watch for order data changes to recalculate countdown when updatedAt becomes available
watch(
  () => orderData.updatedAt,
  (newUpdatedAt) => {
    if (newUpdatedAt && tradeStatus.value) {
      // Recalculate countdown with real data
      if (tradeStatus.value === 'BankAccountAssigned') {
        startCountdown('WAITING_FOR_CONFIRMATION');
      } else if (tradeStatus.value === 'Pending') {
        startCountdown('WAITING_FOR_AGENT_RESPONSE');
      } else if (tradeStatus.value === 'PaymentMade') {
        startCountdown('WAITING_FOR_AGENT_CONFIRMATION');
      }
    }
  }
);
watch(
  () => orderData.tradeType,
  () => {
    console.log('tradeType changed:', orderData.tradeType);
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  verifyEntryCodeFromUrl();
});
</script>

<style scoped>
.container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.wrapper {
  width: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.countdown-container {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(255, 152, 0, 0.05);
  border: 1px solid rgba(255, 152, 0, 0.2);
}

.countdown-label {
  font-size: 0.9rem;
  color: #ff9800;
  margin-bottom: 5px;
}

.countdown-timer {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff9800;
  margin-bottom: 5px;
}
</style>
