import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type SellPaymentMadeProps = {
  entryCode: string;
};

type SellPaymentMadeRes = unknown;

const useSellPaymentMade = ({ ...useProps }: UseProps<SellPaymentMadeRes>) => {
  const vueRequest = requestProvider<SellPaymentMadeRes, SellPaymentMadeProps>(
    (props: SellPaymentMadeProps) => {
      const request = externalAxiosProvider
        .put('/proxy-orders/payment-confirmed', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useSellPaymentMade };
