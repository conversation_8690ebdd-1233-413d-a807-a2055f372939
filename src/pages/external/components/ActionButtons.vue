<template>
  <div class="action-buttons">
    <!-- Confirm Payment Button -->
    <q-btn
      :color="getConfirmButtonColor"
      class="full-width q-py-sm q-mb-md modern-submit-btn"
      style="
        border-radius: 12px;
        font-weight: 600;
        height: 56px;
        position: relative;
        overflow: hidden;
      "
      :label="getConfirmButtonLabel"
      :icon="getConfirmButtonIcon"
      @click="$emit('confirm-payment')"
      :disable="getConfirmButtonDisabled"
      :loading="getConfirmButtonLoading"
      no-caps
    >
      <!-- Loading overlay with text -->
      <div
        v-if="getConfirmButtonLoading"
        class="loading-overlay absolute-full flex items-center justify-center"
        style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(2px)"
      >
        <q-spinner-dots color="white" size="1.5em" class="q-mr-sm" />
        <span class="text-body2 text-weight-medium">{{
          getLoadingText
        }}</span>
      </div>

      <!-- Success animation overlay -->
      <div
        v-if="confirmedPaid && !getConfirmButtonLoading"
        class="success-overlay absolute-full flex items-center justify-center"
        style="background: rgba(76, 175, 80, 0.1)"
      >
        <q-icon name="check_circle" size="1.2em" class="q-mr-xs" />
        <span class="text-body2 text-weight-medium">{{
          t('externalPaymentDetailPage.confirmed')
        }}</span>
      </div>
    </q-btn>

    <!-- Payment Proof Button (only for buy orders) -->
    <q-btn
      v-if="!isSellOrder"
      :color="paymentProofUrl ? 'secondary' : 'blue-5'"
      class="full-width q-py-sm q-mb-md"
      style="border-radius: 8px; font-weight: 500; height: 54px"
      :label="
        paymentProofUrl
          ? t('externalPaymentDetailPage.uploaded')
          : t('externalPaymentDetailPage.uploadPaymentProofButton')
      "
      :icon-right="paymentProofUrl ? 'visibility' : 'upload_file'"
      @click="
        paymentProofUrl
          ? $emit('show-payment-proof')
          : $emit('open-payment-proof-dialog')
      "
      :loading="uploadingPaymentProof"
      :disable="confirmedProof"
    />

    <!-- Dispute Button (for sell orders) -->
    <q-btn
      v-if="isSellOrder && !isOrderCompleted"
      color="negative"
      outline
      class="full-width q-py-sm q-mb-md"
      style="border-radius: 8px; font-weight: 500; height: 54px"
      :label="t('externalPaymentDetailPage.disputeButton')"
      icon="report_problem"
      @click="$emit('open-dispute')"
      :loading="disputeLoading"
      :disable="isDisputeDisabled"
    />

    <!-- Hidden file input for payment proof upload -->
    <input
      type="file"
      ref="paymentProofInput"
      accept="image/*"
      style="display: none"
      @change="$emit('payment-proof-selected', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { TradeStatusEnum, TradeTypeEnum } from 'src/utils/enums';
import stringToEnumValue from '../../../utils/stringToEnumValue';

interface Props {
  tradeStatus: string;
  confirmedPaid: boolean;
  confirmedProof: boolean;
  loading: boolean;
  uploadingPaymentProof: boolean;
  paymentProofUrl: string | null;
  tradeType?: TradeTypeEnum;
  disputeLoading?: boolean;
  disputeStatus?: string;
}

const props = defineProps<Props>();

defineEmits<{
  'confirm-payment': [];
  'show-payment-proof': [];
  'open-payment-proof-dialog': [];
  'payment-proof-selected': [event: Event];
  'open-dispute': [];
}>();

const { t } = useI18n();
const paymentProofInput = ref<HTMLInputElement | null>(null);
const isSellOrder = ref(false);
const isOrderCompleted = computed(() =>
  ['Completed', 'Cancelled', 'Expired'].includes(props.tradeStatus)
);
const isDisputeDisabled = computed(
  () => props.disputeStatus !== 'None' || props.disputeLoading
);

watch(
  () => props.tradeType,
  (newStatus) => {
    isSellOrder.value = newStatus === TradeTypeEnum.Sell;
  },
  { immediate: true }
);

// Convert to computed properties for proper reactivity
const getConfirmButtonDisabled = computed(() => {
  if (props.confirmedPaid) return true; // Always disabled after user clicks

  if (isSellOrder.value) {
    // Sell order: disabled until PaymentMade status
    return (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) !==
      TradeStatusEnum.PaymentMade
    );
  } else {
    // Buy order: enabled at BankAccountAssigned status
    return (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) !==
      TradeStatusEnum.BankAccountAssigned
    );
  }
});

// Get confirm button label based on status
const getConfirmButtonLabel = computed(() => {
  if (props.confirmedPaid) {
    return t('externalPaymentDetailPage.confirmed');
  }

  if (props.tradeType === TradeTypeEnum.Sell) {
    // Sell order logic
    if (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.PaymentMade
    ) {
      return t('externalPaymentDetailPage.confirmPaymentReceivedButton');
    } else {
      return t('externalPaymentDetailPage.waitingForAgentPayment');
    }
  } else {
    // Buy order logic
    if (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.BankAccountAssigned
    ) {
      return t('externalPaymentDetailPage.confirmPaymentButton');
    } else if (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.PaymentMade
    ) {
      return t('externalPaymentDetailPage.waitingForConfirmation');
    } else {
      return t('externalPaymentDetailPage.waitingForAgent');
    }
  }
});

// Get confirm button icon based on status
const getConfirmButtonIcon = computed(() => {
  if (props.confirmedPaid) {
    return 'check_circle';
  }

  if (props.tradeType === TradeTypeEnum.Sell) {
    return stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.PaymentMade
      ? ''
      : 'hourglass_empty';
  } else {
    if (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.BankAccountAssigned
    ) {
      return '';
    } else {
      return 'hourglass_empty';
    }
  }
});

// Get confirm button loading state
const getConfirmButtonLoading = computed(() => {
  if (props.tradeType === TradeTypeEnum.Sell) {
    return (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.BankAccountAssigned
    );
  } else {
    return (
      stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.PaymentMade
    );
  }
});

// Get confirm button color based on status
const getConfirmButtonColor = computed(() => {
  if (props.confirmedPaid) {
    return 'positive';
  }

  if (props.tradeType === TradeTypeEnum.Sell) {
    return stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.PaymentMade
      ? 'primary'
      : 'orange';
  } else {
    return stringToEnumValue(TradeStatusEnum, props.tradeStatus) ===
      TradeStatusEnum.BankAccountAssigned
      ? 'primary'
      : 'orange';
  }
});

// Get loading text based on status
const getLoadingText = computed(() => {
  if (props.tradeStatus === 'PaymentMade') {
    return t('externalPaymentDetailPage.processingConfirmation');
  }
  return t('externalPaymentDetailPage.processing');
});

defineExpose({
  paymentProofInput,
});
</script>

<style scoped>
/* Modern Submit Button Styles */
.modern-submit-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.modern-submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.loading-overlay {
  animation: pulse 2s infinite;
}

.success-overlay {
  animation: successPulse 0.6s ease-out;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes successPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
