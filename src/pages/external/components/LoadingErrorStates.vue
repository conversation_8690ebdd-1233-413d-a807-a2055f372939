<template>
  <!-- Loading state -->
  <div v-if="verifying" class="full-width flex flex-center column">
    <q-spinner color="primary" size="3em" />
    <div class="q-mt-md text-subtitle1">
      {{ t('externalPaymentDetailPage.verifying') }}
    </div>
  </div>

  <!-- Agent not accepting order error -->
  <div
    v-else-if="agentNotAcceptingOrderError"
    class="full-width flex flex-center column"
  >
    <q-icon name="error" color="negative" size="3em" />
    <div class="q-mt-md text-subtitle1 text-negative">
      {{ t('externalPaymentDetailPage.agentNotAcceptingOrderError') }}
    </div>
  </div>

  <!-- Order expired error -->
  <div v-else-if="orderExpiredError" class="full-width flex flex-center column">
    <q-icon name="error" color="negative" size="3em" />
    <div class="q-mt-md text-subtitle1 text-negative">
      {{ t('externalPaymentDetailPage.orderExpiredError') }}
    </div>
  </div>

  <!-- Verification error -->
  <div v-else-if="verificationError" class="full-width flex flex-center column">
    <q-icon name="error" color="negative" size="3em" />
    <div class="q-mt-md text-subtitle1 text-negative">
      {{ t('externalPaymentDetailPage.verificationError') }}
    </div>
    <q-btn
      color="primary"
      class="q-mt-lg"
      :label="t('externalPaymentDetailPage.tryAgain')"
      @click="$emit('retry')"
    />
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Props {
  verifying: boolean;
  agentNotAcceptingOrderError: boolean;
  orderExpiredError: boolean;
  verificationError: boolean;
  orderNumber: string;
}

defineProps<Props>();
defineEmits<{
  retry: [];
}>();

const { t } = useI18n();
</script>
