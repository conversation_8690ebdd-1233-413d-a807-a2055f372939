<template>
  <div
    :class="[
      'payment-notice rounded-borders q-pa-lg q-mb-lg',
      getPaymentNoticeClass(),
    ]"
    style="border-radius: 16px; position: relative"
  >
    <div class="row">
      <div class="col-auto q-mr-md">
        <q-icon
          :name="getPaymentNoticeIcon()"
          :color="getPaymentNoticeIconColor()"
          size="2rem"
        />
      </div>
      <div class="col">
        <div
          class="text-subtitle1 text-weight-bold"
          :class="getPaymentNoticeTitleColor()"
        >
          {{ getPaymentNoticeTitle() }}
        </div>
        <div class="text-body2 q-mt-sm text-grey-8">
          {{ getStatusMessage() }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { useI18n } from 'vue-i18n';
import { TradeTypeEnum, TradeStatusEnum } from 'src/utils/enums';
import { reactive } from 'vue-demi';
import { watch } from 'vue';
import stringToEnumValue from '../../../utils/stringToEnumValue';

interface Props {
  tradeStatus: string;
  tradeType?: TradeTypeEnum;
}

const props = defineProps<Props>();

const { t } = useI18n();

// Get status-based message
const getStatusMessage = () => {
  if (props.tradeType === TradeTypeEnum.Sell) {
    // Sell order messages
    switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
      case TradeStatusEnum.Pending:
        return t('externalPaymentDetailPage.sellOrderPendingMessage');
      case TradeStatusEnum.BankAccountAssigned:
        return t('externalPaymentDetailPage.sellWaitingForAgentPaymentMessage');
      case TradeStatusEnum.PaymentMade:
        return t('externalPaymentDetailPage.sellConfirmReceiptMessage');
      case TradeStatusEnum.PaymentConfirmed:
        return t('externalPaymentDetailPage.sellPaymentConfirmedMessage');
      case TradeStatusEnum.Completed:
        return t('externalPaymentDetailPage.sellOrderCompletedMessage');
      case TradeStatusEnum.Cancelled:
        return t('externalPaymentDetailPage.sellOrderCancelledMessage');
      case TradeStatusEnum.Expired:
        return t('externalPaymentDetailPage.sellOrderExpiredMessage');
      default:
        return t('externalPaymentDetailPage.sellPaymentInstructions');
    }
  } else {
    // Buy order messages
    switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
      case TradeStatusEnum.Pending:
        return t('externalPaymentDetailPage.buyOrderPendingMessage');
      case TradeStatusEnum.Accepted:
        return t('externalPaymentDetailPage.buyOrderAcceptedMessage');
      case TradeStatusEnum.BankAccountAssigned:
        return t('externalPaymentDetailPage.buyMakePaymentMessage');
      case TradeStatusEnum.PaymentMade:
        return t('externalPaymentDetailPage.buyWaitingForConfirmationMessage');
      case TradeStatusEnum.PaymentConfirmed:
        return t('externalPaymentDetailPage.buyPaymentConfirmedMessage');
      case TradeStatusEnum.Completed:
        return t('externalPaymentDetailPage.buyOrderCompletedMessage');
      case TradeStatusEnum.Cancelled:
        return t('externalPaymentDetailPage.buyOrderCancelledMessage');
      case TradeStatusEnum.Expired:
        return t('externalPaymentDetailPage.buyOrderExpiredMessage');
      default:
        return t('externalPaymentDetailPage.paymentInstructions');
    }
  }
};

// Get payment notice class based on status
const getPaymentNoticeClass = () => {
  switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
    case TradeStatusEnum.Completed:
      return 'bg-green-1';
    case TradeStatusEnum.Cancelled:
    case TradeStatusEnum.Expired:
      return 'bg-red-1';
    case TradeStatusEnum.PaymentMade:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'bg-purple-1'
        : 'bg-blue-1';
    case TradeStatusEnum.PaymentConfirmed:
      return 'bg-teal-1';
    case TradeStatusEnum.BankAccountAssigned:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'bg-orange-1'
        : 'bg-primary-1';
    default:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'bg-indigo-1'
        : 'bg-orange-1';
  }
};

// Get payment notice icon based on status
const getPaymentNoticeIcon = () => {
  switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
    case TradeStatusEnum.Pending:
      return 'schedule';
    case TradeStatusEnum.Accepted:
      return 'task_alt';
    case TradeStatusEnum.BankAccountAssigned:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'hourglass_empty'
        : 'payment';
    case TradeStatusEnum.PaymentMade:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'notifications_active'
        : 'hourglass_empty';
    case TradeStatusEnum.PaymentConfirmed:
      return 'verified';
    case TradeStatusEnum.Completed:
      return 'check_circle';
    case TradeStatusEnum.Cancelled:
    case TradeStatusEnum.Expired:
      return 'cancel';
    default:
      return 'info';
  }
};

// Get payment notice icon color based on status
const getPaymentNoticeIconColor = () => {
  switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
    case TradeStatusEnum.Completed:
      return 'green-7';
    case TradeStatusEnum.Cancelled:
    case TradeStatusEnum.Expired:
      return 'red-7';
    case TradeStatusEnum.PaymentMade:
      return props.tradeType === TradeTypeEnum.Sell ? 'purple-7' : 'blue-7';
    case TradeStatusEnum.PaymentConfirmed:
      return 'teal-7';
    case TradeStatusEnum.BankAccountAssigned:
      return props.tradeType === TradeTypeEnum.Sell ? 'orange-7' : 'primary';
    default:
      return props.tradeType === TradeTypeEnum.Sell ? 'indigo-7' : 'orange-7';
  }
};

// Get payment notice title color based on status
const getPaymentNoticeTitleColor = () => {
  switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
    case TradeStatusEnum.Completed:
      return 'text-green-8';
    case TradeStatusEnum.Cancelled:
    case TradeStatusEnum.Expired:
      return 'text-red-8';
    case TradeStatusEnum.PaymentMade:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'text-purple-8'
        : 'text-blue-8';
    case TradeStatusEnum.PaymentConfirmed:
      return 'text-teal-8';
    case TradeStatusEnum.BankAccountAssigned:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'text-orange-8'
        : 'text-primary';
    default:
      return props.tradeType === TradeTypeEnum.Sell
        ? 'text-indigo-8'
        : 'text-orange-8';
  }
};

// Get payment notice title based on status
const getPaymentNoticeTitle = () => {
  if (props.tradeType === TradeTypeEnum.Sell) {
    // Sell order titles
    switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
      case TradeStatusEnum.Pending:
        return t('externalPaymentDetailPage.sellOrderPending');
      case TradeStatusEnum.BankAccountAssigned:
        return t('externalPaymentDetailPage.sellWaitingForAgentPayment');
      case TradeStatusEnum.PaymentMade:
        return t('externalPaymentDetailPage.sellConfirmReceipt');
      case TradeStatusEnum.PaymentConfirmed:
        return t('externalPaymentDetailPage.sellPaymentConfirmed');
      case TradeStatusEnum.Completed:
        return t('externalPaymentDetailPage.sellOrderCompleted');
      case TradeStatusEnum.Cancelled:
        return t('externalPaymentDetailPage.sellOrderCancelled');
      case TradeStatusEnum.Expired:
        return t('externalPaymentDetailPage.sellOrderExpired');
      default:
        return t('externalPaymentDetailPage.sellPaymentNotice');
    }
  } else {
    // Buy order titles
    switch (stringToEnumValue(TradeStatusEnum, props.tradeStatus!)) {
      case TradeStatusEnum.Pending:
        return t('externalPaymentDetailPage.buyOrderPending');
      case TradeStatusEnum.Accepted:
        return t('externalPaymentDetailPage.buyOrderAccepted');
      case TradeStatusEnum.BankAccountAssigned:
        return t('externalPaymentDetailPage.buyMakePayment');
      case TradeStatusEnum.PaymentMade:
        return t('externalPaymentDetailPage.buyWaitingForConfirmation');
      case TradeStatusEnum.PaymentConfirmed:
        return t('externalPaymentDetailPage.buyPaymentConfirmed');
      case TradeStatusEnum.Completed:
        return t('externalPaymentDetailPage.buyOrderCompleted');
      case TradeStatusEnum.Cancelled:
        return t('externalPaymentDetailPage.buyOrderCancelled');
      case TradeStatusEnum.Expired:
        return t('externalPaymentDetailPage.buyOrderExpired');
      default:
        return t('externalPaymentDetailPage.paymentNotice');
    }
  }
};
</script>

<style scoped>
.payment-notice {
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

body.isDark .payment-notice {
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
