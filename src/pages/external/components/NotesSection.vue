<template>
  <div class="q-mt-xl">
    <div class="text-subtitle1 q-mb-sm">
      {{ t('externalPaymentDetailPage.notesTitle') }}
    </div>
    <ul class="q-pl-md">
      <li
        v-for="(note, index) in notes"
        :key="index"
        class="q-mb-xs text-grey-8"
      >
        {{ note }}
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const notes = computed(() => [
  t('externalPaymentDetailPage.note1'),
  t('externalPaymentDetailPage.note2'),
  t('externalPaymentDetailPage.note3'),
  t('externalPaymentDetailPage.note4'),
  t('externalPaymentDetailPage.note5'),
  t('externalPaymentDetailPage.note6'),
  t('externalPaymentDetailPage.note7'),
]);
</script>
