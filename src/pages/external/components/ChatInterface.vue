<template>
  <div
    :class="[
      'chat-container',
      tradeStatus === 'Completed' ? 'is-completed' : 'is-not-completed',
    ]"
  >
    <div class="chat-header q-pa-sm flex items-center justify-between">
      <div class="text-weight-bold">
        {{ t('externalPaymentDetailPage.customerService') }}
      </div>
      <q-badge
        color="green"
        :label="t('externalPaymentDetailPage.online')"
      />
    </div>

    <div class="chat-messages q-px-sm q-py-md" ref="chatMessagesRef">
      <div
        v-for="(message, index) in chatMessages"
        :key="index"
        :class="[
          'message-item q-mb-md',
          getMessageClass(message.senderRole),
        ]"
      >
        <!-- Support Team Label -->
        <div
          v-if="message.senderRole === SenderRole.SupportTeam"
          class="support-team-label q-mb-xs"
        >
          <q-chip
            color="purple"
            text-color="white"
            icon="support_agent"
            size="sm"
            class="support-chip"
          >
            Support Team
          </q-chip>
        </div>

        <!-- Agent Label -->
        <div
          v-else-if="message.senderRole === SenderRole.Agent"
          class="agent-label q-mb-xs"
        >
          <q-chip
            color="green"
            text-color="white"
            icon="person"
            size="sm"
            class="agent-chip"
          >
            Agent
          </q-chip>
        </div>

        <div class="message-content q-pa-md">
          <!-- Text message -->
          <div v-if="!message.isImage" class="message-text">
            {{ message.content }}
          </div>
          <!-- Image message -->
          <div v-else class="image-message">
            <q-img
              :src="message.content"
              spinner-color="primary"
              style="
                width: 200px;
                height: 200px;
                border-radius: 8px;
                cursor: pointer;
              "
              @load="$emit('scroll-to-bottom')"
              @click="$emit('open-image-preview', message.content)"
              fit="contain"
              class="message-image"
            >
              <div class="absolute-bottom text-subtitle2 text-center">
                <q-icon name="zoom_in" size="sm" />
                Click to view
              </div>
            </q-img>
          </div>
        </div>
        <div class="message-time text-caption">
          {{ message.time }}
        </div>
      </div>
    </div>

    <!-- Hidden file input for image upload -->
    <input
      type="file"
      ref="fileInput"
      accept="image/*"
      style="display: none"
      @change="$emit('file-selected', $event)"
    />

    <div class="chat-input q-pa-sm">
      <q-input
        :model-value="messageInput"
        @update:model-value="$emit('update:message-input', $event)"
        outlined
        dense
        :placeholder="t('externalPaymentDetailPage.typeMessage')"
        @keyup.enter="$emit('send-message')"
      >
        <template v-slot:before>
          <q-btn
            round
            dense
            flat
            icon="attach_file"
            color="primary"
            @click="$emit('open-file-dialog')"
          />
        </template>
        <template v-slot:after>
          <q-btn
            round
            dense
            flat
            icon="send"
            color="primary"
            @click="$emit('send-message')"
          />
        </template>
      </q-input>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { SenderRole } from 'src/utils/enums';
import type { ChatMessage } from '../composables/useChatFunctionality';

interface Props {
  tradeStatus: string;
  chatMessages: ChatMessage[];
  messageInput: string;
  getMessageClass: (senderRole: SenderRole) => string;
}

defineProps<Props>();

defineEmits<{
  'update:message-input': [value: string];
  'send-message': [];
  'open-file-dialog': [];
  'file-selected': [event: Event];
  'open-image-preview': [imageUrl: string];
  'scroll-to-bottom': [];
}>();

const { t } = useI18n();
const chatMessagesRef = ref<HTMLElement | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);

defineExpose({
  chatMessagesRef,
  fileInput,
});
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.is-completed {
  max-height: 860px;
}

.is-not-completed {
  max-height: 840px;
}

body.isDark .chat-container {
  border-color: #424242;
}

.chat-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

body.isDark .chat-header {
  background-color: #333333;
  border-color: #424242;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  background-color: #f9f9f9;
  padding-right: 8px; /* Added padding to fix scrollbar overlap */
  scrollbar-width: thin;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

body.isDark .chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

body.isDark .chat-messages {
  background-color: #1e1e1e;
}

.chat-input {
  border-top: 1px solid #e0e0e0;
  background-color: #f5f5f5;
}

body.isDark .chat-input {
  border-color: #424242;
  background-color: #333333;
}

.message-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 16px; /* Increased spacing between messages */
}

.message-content {
  border-radius: 16px;
  word-break: break-word;
  width: fit-content;
  max-width: 75%; /* Ensure content doesn't overflow */
  overflow: hidden; /* Hide overflow content */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.message-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.message-text {
  line-height: 1.4;
  font-size: 0.95rem;
}

.image-message {
  position: relative;
}

.message-image {
  transition: transform 0.2s ease;
}

.message-image:hover {
  transform: scale(1.02);
}

/* Support Team Messages */
.support-team-message {
  align-items: flex-start;
}

.support-team-message .message-content {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
  color: white;
  border-top-left-radius: 4px;
  box-shadow: 0 3px 12px rgba(156, 39, 176, 0.4);
  border: 2px solid rgba(156, 39, 176, 0.3);
}

.support-chip {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Agent Messages */
.agent-message {
  align-items: flex-start;
}

.agent-message .message-content {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.agent-chip {
  font-size: 0.75rem;
  font-weight: 600;
}

/* User Messages */
.user-message {
  align-items: flex-end;
}

.user-message .message-content {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-top-right-radius: 4px;
  box-shadow: 0 2px 10px rgba(33, 150, 243, 0.3);
}

/* Dark mode adjustments */
body.isDark .support-team-message .message-content {
  background: linear-gradient(135deg, #7b1fa2 0%, #4a148c 100%);
  box-shadow: 0 3px 12px rgba(123, 31, 162, 0.5);
}

body.isDark .agent-message .message-content {
  background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
  box-shadow: 0 2px 10px rgba(56, 142, 60, 0.4);
}

body.isDark .user-message .message-content {
  background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
  box-shadow: 0 2px 10px rgba(25, 118, 210, 0.4);
}

.message-time {
  margin-top: 2px;
  font-size: 0.7rem;
}
</style>
