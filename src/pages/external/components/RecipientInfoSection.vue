<template>
  <div class="recipient-info q-mt-md q-pb-md">
    <div class="text-subtitle1 text-weight-medium text-grey-8 q-mb-sm">
      {{ getSectionTitle() }}
    </div>

    <div class="row q-col-gutter-md">
      <!-- Recipient Name -->
      <div class="col-12">
        <div class="recipient-account bg-grey-1 rounded-borders q-pa-md">
          <div class="row items-center justify-between">
            <div class="col">
              <div class="text-caption text-grey-7">
                {{ getNameLabel() }}
              </div>
              <div class="text-body2 text-weight-medium q-mt-xs">
                {{ orderData.payerBankAccountName }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { computed, watch } from 'vue';
import type { OrderData } from '../composables/useOrderData';
import { TradeTypeEnum } from 'src/utils/enums';
import { useTradeStore } from '../../../stores';
import { reactive } from 'vue-demi';
import stringToEnumValue from '../../../utils/stringToEnumValue';

interface Props {
  orderData: OrderData;
  tradeType?: string;
}

const props = defineProps<Props>();

const { t } = useI18n();

// Get appropriate labels and instructions based on order type
const getSectionTitle = () => {
  console.log('Changed:', props.tradeType);
  return stringToEnumValue(TradeTypeEnum, props.tradeType ?? 'Buy') ===
    TradeTypeEnum.Sell
    ? t('externalPaymentDetailPage.payeeInfo')
    : t('externalPaymentDetailPage.payerInfo');
};

const getNameLabel = () => {
  return stringToEnumValue(TradeTypeEnum, props.tradeType ?? 'Buy') ===
    TradeTypeEnum.Sell
    ? t('externalPaymentDetailPage.payeeInfo')
    : t('externalPaymentDetailPage.payerName');
};
</script>
