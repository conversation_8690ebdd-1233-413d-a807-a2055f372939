<template>
  <div class="modern-order-container q-mx-auto" style="max-width: 700px">
    <!-- Order Summary Card -->
    <div
      class="order-summary-card bg-white shadow-8 rounded-borders q-mb-lg"
      style="border-radius: 16px; overflow: hidden"
    >
      <!-- Header Banner -->
      <div
        class="order-header bg-gradient text-white q-pa-md"
        style="
          background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
          position: relative;
          overflow: hidden;
        "
      >
        <div class="row items-center justify-between q-py-sm">
          <div class="col-auto">
            <div class="text-h6 text-weight-medium">
              {{ t('externalPaymentDetailPage.orderDetails') }}
            </div>
            <div class="text-caption q-mt-xs">
              {{ orderData.orderTime }}
            </div>
          </div>

          <div class="col-auto flex justify-center items-center gap-md">
            <div v-if="orderData.disputeStatus !== 'None'">
              <q-icon name="warning" color="warning">
                <q-tooltip anchor="top middle" self="bottom middle">
                  There is a dispute on this transaction.
                </q-tooltip>
              </q-icon>
            </div>

            <trade-status-label
              :status="
                stringToEnumValue(TradeStatusEnum, orderData.orderStatus)
              "
            />
          </div>
        </div>
        <!-- Decorative elements -->
        <div
          class="header-decoration"
          style="
            position: absolute;
            top: -30px;
            right: -30px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            opacity: 0.1;
            background: radial-gradient(circle, #ffffff 0%, transparent 70%);
          "
        ></div>
        <div
          class="header-decoration"
          style="
            position: absolute;
            bottom: -40px;
            left: -40px;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            opacity: 0.1;
            background: radial-gradient(circle, #ffffff 0%, transparent 70%);
          "
        ></div>
      </div>

      <!-- Transaction ID and Time Section -->
      <div class="q-pa-lg">
        <div
          class="row justify-between items-center q-pa-md bg-grey-1 rounded-borders"
        >
          <div class="col-auto">
            <div class="text-subtitle1 text-weight-medium text-grey-8">
              {{ $t('externalPaymentDetailPage.orderNumber') }}
            </div>
            <div class="row items-center">
              <div class="text-h6 text-primary">
                {{ orderData.orderNumber }}
              </div>
              <q-btn
                flat
                round
                dense
                color="grey-7"
                icon="content_copy"
                size="sm"
                class="q-ml-xs"
                @click="
                  $emit(
                    'copy-order-number',
                    orderData.orderNumber,
                    'copiedOrderNumber'
                  )
                "
              />
            </div>
          </div>
        </div>
      </div>
      <q-separator spaced />

      <!-- Order Overview -->
      <div class="order-overview q-pa-lg">
        <!-- Amount Cards -->
        <div class="row q-col-gutter-md q-mb-md">
          <!-- Payment amount -->
          <div class="col-12 col-md-6">
            <div
              class="amount-card bg-indigo-1 rounded-borders q-pa-md"
              style="border-radius: 12px"
            >
              <div class="row items-center">
                <div class="col">
                  <div class="text-caption text-grey-8">
                    {{
                      isSellOrder
                        ? t('externalPaymentDetailPage.fiatAmountWillReceive')
                        : t('externalPaymentDetailPage.fiatAmountHaveToBePaid')
                    }}
                  </div>
                  <div
                    class="text-h5 flex items-center text-weight-bold text-indigo-9 q-mt-xs"
                  >
                    {{ orderData.paymentAmount }}
                    <q-btn
                      flat
                      round
                      dense
                      color="grey-7"
                      icon="content_copy"
                      size="sm"
                      class="q-ml-xs"
                      @click="$emit('copy-fiat-amount')"
                    />
                  </div>
                </div>

                <div class="col-auto">
                  <q-icon name="payments" color="indigo-5" size="2rem" />
                </div>
              </div>
            </div>
          </div>

          <!-- Crypto amount -->
          <div class="col-12 col-md-6">
            <div
              class="amount-card bg-blue-1 rounded-borders q-pa-md"
              style="border-radius: 12px"
            >
              <div class="row items-center">
                <div class="col">
                  <div class="text-caption text-grey-8">
                    {{
                      isSellOrder
                        ? t(
                            'externalPaymentDetailPage.cryptoAmountHaveToBePaid'
                          )
                        : t('externalPaymentDetailPage.cryptoAmountWillReceive')
                    }}
                  </div>
                  <div class="text-h5 text-weight-bold text-blue-9 q-mt-xs">
                    {{ orderData.cryptoAmount }}
                  </div>
                </div>
                <div class="col-auto">
                  <q-icon name="currency_bitcoin" color="blue-5" size="2rem" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Divider -->
        <q-separator class="q-my-md" />

        <!-- Transaction Details -->
        <div class="transaction-details q-pa-md">
          <div class="details-list">
            <!-- Header -->
            <div class="payment-header q-pt-md q-mb-md">
              <div class="row items-center">
                <q-icon
                  name="account_balance"
                  color="primary"
                  size="1.8rem"
                  class="q-mr-sm"
                />
                <div class="text-h6 text-weight-medium text-primary">
                  {{ t('externalPaymentDetailPage.paymentDetailsTitle') }}
                </div>
              </div>
              <div class="text-caption text-grey-7 q-mt-sm">
                {{
                  isSellOrder
                    ? t('externalPaymentDetailPage.sellPaymentDetailsSubtitle')
                    : t('externalPaymentDetailPage.paymentDetailsSubtitle')
                }}
              </div>
            </div>

            <!-- Bank Info Card -->
            <bank-info-section
              :order-data="orderData"
              @copy-recipient-bank-name="$emit('copy-recipient-bank-name')"
              @copy-recipient-account="$emit('copy-recipient-account')"
              @copy-recipient-branch-code="$emit('copy-recipient-branch-code')"
              @copy-recipient-name="$emit('copy-recipient-name')"
            />

            <!-- Recipient Information -->
            <recipient-info-section
              :order-data="orderData"
              :trade-type="orderData.tradeTypeDesc"
              @copy-payer-bank-account-name="
                $emit('copy-payer-bank-account-name')
              "
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { TradeStatusEnum } from 'src/utils/enums';
import stringToEnumValue from 'src/utils/stringToEnumValue';
import TradeStatusLabel from 'src/components/TradeStatusLabel.vue';
import BankInfoSection from './BankInfoSection.vue';
import RecipientInfoSection from './RecipientInfoSection.vue';
import type { OrderData } from '../composables/useOrderData';
import { computed } from 'vue';
interface Props {
  orderData: OrderData;
}
const props = defineProps<Props>();
const isSellOrder = computed(() => props.orderData.tradeTypeDesc === 'Sell');

defineEmits<{
  'copy-order-number': [orderNumber: string, messageKey: string];
  'copy-fiat-amount': [];
  'copy-recipient-bank-name': [];
  'copy-recipient-account': [];
  'copy-recipient-branch-code': [];
  'copy-recipient-name': [];
  'copy-payer-bank-account-name': [];
}>();

const { t } = useI18n();
</script>
