<template>
  <div
    class="bank-info bg-blue-1 rounded-borders q-pa-md q-mb-md"
    style="border-radius: 12px"
  >
    <div class="row q-col-gutter-md">
      <div class="col-12 col-sm-6">
        <!-- Bank Name Card with Copy Button -->
        <div
          class="account-number-card rounded-borders"
          style="border-radius: 12px"
        >
          <div class="text-caption text-grey-7">
            {{ getBankNameLabel() }}
          </div>
          <div
            class="row items-center justify-between q-mt-sm"
          >
            <div class="text-body1 text-weight-bold">
              {{ orderData.recipientBank }}
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
                dense
                icon="content_copy"
                size="xs"
                color="grey-7"
                @click="$emit('copy-recipient-bank-name')"
              >
                <q-tooltip>{{
                  t(
                    'externalPaymentDetailPage.copyRecipientBankName'
                  )
                }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6">
        <!-- Account Number Card with Copy Button -->
        <div
          class="account-number-card rounded-borders"
          style="border-radius: 12px"
        >
          <div class="text-caption text-grey-7">
            {{ getAccountNumberLabel() }}
          </div>
          <div
            class="row items-center justify-between q-mt-sm"
          >
            <div class="text-body1 text-weight-bold">
              {{ orderData.recipientAccount }}
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
                dense
                icon="content_copy"
                size="xs"
                color="grey-7"
                @click="$emit('copy-recipient-account')"
              >
                <q-tooltip>{{
                  t(
                    'externalPaymentDetailPage.copyRecipientAccount'
                  )
                }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row q-col-gutter-md">
      <div class="col-12 col-sm-6">
        <div class="text-caption text-grey-7">
          {{ getBranchCodeLabel() }}
        </div>
        <div class="row items-center justify-between q-mt-sm">
          <div class="text-body2 text-weight-medium q-mt-xs">
            {{ orderData.recipientBranchCode }}
          </div>
          <div class="col-auto">
            <q-btn
              flat
              round
              dense
              icon="content_copy"
              size="xs"
              color="grey-7"
              @click="$emit('copy-recipient-branch-code')"
            >
              <q-tooltip>{{
                t('externalPaymentDetailPage.copyBranchCode')
              }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6">
        <div class="text-caption text-grey-7">
          {{ getAccountNameLabel() }}
        </div>
        <div class="row items-center justify-between q-mt-sm">
          <div
            class="text-body2 text-weight-medium q-mt-xs text-truncate"
          >
            {{ orderData.recipientName }}
          </div>
          <div class="col-auto">
            <q-btn
              flat
              round
              dense
              icon="content_copy"
              size="xs"
              color="grey-7"
              @click="$emit('copy-recipient-name')"
            >
              <q-tooltip>{{
                t(
                  'externalPaymentDetailPage.copyRecipientName'
                )
              }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';
import type { OrderData } from '../composables/useOrderData';
import { TradeTypeEnum } from 'src/utils/enums';

interface Props {
  orderData: OrderData;
  tradeType?: TradeTypeEnum;
}

const props = defineProps<Props>();

defineEmits<{
  'copy-recipient-bank-name': [];
  'copy-recipient-account': [];
  'copy-recipient-branch-code': [];
  'copy-recipient-name': [];
}>();

const { t } = useI18n();

const isSellOrder = computed(() => props.tradeType === TradeTypeEnum.Sell);

// Get appropriate labels based on order type
const getBankNameLabel = () => {
  return isSellOrder.value
    ? t('externalPaymentDetailPage.yourBankName')
    : t('externalPaymentDetailPage.paymentDetailsBankName');
};

const getAccountNumberLabel = () => {
  return isSellOrder.value
    ? t('externalPaymentDetailPage.yourAccountNumber')
    : t('externalPaymentDetailPage.paymentDetailsAccountNumber');
};

const getBranchCodeLabel = () => {
  return isSellOrder.value
    ? t('externalPaymentDetailPage.yourBranchCode')
    : t('externalPaymentDetailPage.paymentDetailsBranchCode');
};

const getAccountNameLabel = () => {
  return isSellOrder.value
    ? t('externalPaymentDetailPage.yourAccountName')
    : t('externalPaymentDetailPage.paymentDetailsAccountName');
};
</script>
