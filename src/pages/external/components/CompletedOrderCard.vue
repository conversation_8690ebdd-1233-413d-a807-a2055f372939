<template>
  <div
    class="completed-card q-mx-auto bg-white shadow-8 rounded-borders q-pa-none"
    style="max-width: 700px; border-radius: 16px; overflow: hidden"
  >
    <!-- Success Banner -->
    <div
      class="success-banner bg-gradient text-white q-pa-lg text-center relative-position"
      style="
        background: linear-gradient(135deg, #21ba45 0%, #1db954 100%);
        height: 180px;
      "
    >
      <div class="absolute-center">
        <q-icon name="check_circle" size="4rem" class="q-mb-sm" />
        <div class="text-h5 text-weight-bold">
          {{ $t('externalPaymentDetailPage.orderCompleted') }}
        </div>
      </div>
    </div>

    <!-- Transaction Details Container -->
    <div class="transaction-container q-pa-lg">
      <!-- Transaction ID and Time Section -->
      <div class="transaction-header q-mb-lg">
        <div class="row justify-between items-center">
          <div class="col-auto">
            <div class="text-subtitle1 text-weight-medium text-grey-8">
              {{ $t('externalPaymentDetailPage.orderNumber') }}
            </div>
            <div class="row items-center">
              <div class="text-h6 text-primary">
                {{ orderData.orderNumber }}
              </div>
              <q-btn
                flat
                round
                dense
                color="grey-7"
                icon="content_copy"
                size="sm"
                class="q-ml-xs"
                @click="
                  $emit(
                    'copy-order-number',
                    orderData.orderNumber,
                    'copiedOrderNumber'
                  )
                "
              />
            </div>
          </div>
          <div class="col-auto">
            <q-badge color="green-2" text-color="green-10" class="q-pa-sm">
              <q-icon name="check" size="xs" class="q-mr-xs" />
              {{ $t('externalPaymentDetailPage.orderStatus') }}:
              {{ $t('externalPaymentDetailPage.orderCompleted') }}
            </q-badge>
          </div>
        </div>
        <div class="text-caption text-grey-7 q-mt-sm">
          {{ $t('externalPaymentDetailPage.completedAt') }}:
          {{ new Date(orderData.completedAt).toLocaleString() }}
        </div>
      </div>

      <q-separator spaced />

      <!-- Amount Information -->
      <div class="amount-section q-mb-lg">
        <div class="row q-col-gutter-md">
          <!-- Fiat Amount -->
          <div class="col-12 col-sm-6">
            <div class="bg-grey-1 rounded-borders q-pa-md">
              <div class="text-caption text-grey-7">
                {{
                  isSellOrder
                    ? t('externalPaymentDetailPage.fiatAmountReceived')
                    : t('externalPaymentDetailPage.fiatAmountPaid')
                }}
              </div>
              <div class="text-h5 text-weight-bold q-mt-sm text-primary">
                {{ orderData.fiatCurrency }}
                {{ orderData.fiatAmount }}
              </div>
            </div>
          </div>

          <!-- Crypto Amount -->
          <div class="col-12 col-sm-6">
            <div class="bg-grey-1 rounded-borders q-pa-md">
              <div class="text-caption text-grey-7">
                {{
                  isSellOrder
                    ? t('externalPaymentDetailPage.cryptoAmountPaid')
                    : t('externalPaymentDetailPage.cryptoAmountReceived')
                }}
              </div>
              <div class="text-h5 text-weight-bold q-mt-sm text-secondary">
                {{ orderData.cryptoAmount }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <q-separator spaced />

      <!-- Recipient Information -->
      <div class="recipient-section q-mb-lg">
        <div class="text-subtitle1 text-weight-medium text-grey-8 q-mb-md">
          {{ $t('externalPaymentDetailPage.recipientInfo') }}
        </div>

        <div class="bg-grey-1 rounded-borders q-pa-md">
          <div class="row q-col-gutter-md">
            <!-- Bank Info -->
            <div class="col-12 col-sm-6">
              <div class="q-mb-md">
                <div class="text-caption text-grey-7">
                  {{ $t('externalPaymentDetailPage.paymentDetailsBankName') }}
                </div>
                <div class="text-body1 text-weight-medium">
                  {{ orderData.recipientBank }}
                </div>
              </div>

              <div>
                <div class="text-caption text-grey-7">
                  {{ $t('externalPaymentDetailPage.recipientName') }}
                </div>
                <div class="text-body1 text-weight-medium">
                  {{ orderData.recipientName }}
                </div>
              </div>
            </div>

            <!-- Account Number -->
            <div class="col-12 col-sm-6">
              <div>
                <div class="text-caption text-grey-7">
                  {{ $t('externalPaymentDetailPage.recipientAccount') }}
                </div>
                <div class="row items-center">
                  <div class="text-body1 text-weight-medium">
                    {{ orderData.recipientAccount }}
                  </div>
                  <q-btn
                    flat
                    round
                    dense
                    color="grey-7"
                    icon="content_copy"
                    size="sm"
                    class="q-ml-xs"
                    @click="
                      $emit(
                        'copy-account-number',
                        orderData.recipientAccount,
                        'copiedAccountNumber'
                      )
                    "
                  />
                </div>
              </div>
              <div class="q-mt-md">
                <div class="text-caption text-grey-7">
                  {{ $t('externalPaymentDetailPage.paymentDetailsBranchCode') }}
                </div>
                <div class="text-body1 text-weight-medium">
                  {{ orderData.recipientBranchCode }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payer Bank Account Name -->
      <div class="payer-info q-mt-md bg-green-1 rounded-borders q-pa-md">
        <div class="row items-center">
          <q-icon
            name="account_circle"
            color="green-8"
            size="sm"
            class="q-mr-sm"
          />
          <div class="text-body2 text-green-8">
            <span class="text-weight-medium"
              >{{ $t('externalPaymentDetailPage.payerBankAccountName') }}:</span
            >
            {{ orderData.payerBankAccountName }}
          </div>
        </div>
      </div>
      <!-- Help and Support -->
      <div class="help-section text-center q-mt-xl">
        <q-icon
          name="support_agent"
          color="grey-7"
          size="1.5rem"
          class="q-mb-sm"
        />
        <div class="text-body2 text-grey-7">
          {{ $t('externalPaymentDetailPage.needHelp') }}
          <a href="#" class="text-primary">{{
            $t('externalPaymentDetailPage.customerService')
          }}</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import type { OrderData } from '../composables/useOrderData';
import { computed } from 'vue';

interface Props {
  orderData: OrderData;
}

var props = defineProps<Props>();
const isSellOrder = computed(() => props.orderData.tradeTypeDesc === 'Sell');

defineEmits<{
  'copy-order-number': [orderNumber: string, messageKey: string];
  'copy-account-number': [accountNumber: string, messageKey: string];
}>();

const { t } = useI18n();
</script>
