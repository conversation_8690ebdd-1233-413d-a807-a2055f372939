<template>
  <div v-if="tradeStatus === 'Pending'" class="waiting-overlay">
    <div class="waiting-container">
      <div class="waiting-card">
        <q-spinner-puff color="primary" size="60px" />
        <div class="waiting-title">
          {{ t('externalPaymentDetailPage.waitingForAgentMessage') }}
        </div>
        <div class="waiting-subtitle">
          {{ t('externalPaymentDetailPage.agentWillSendDetailsSoon') }}
        </div>
        <vue-countdown
          ref="countdownRef"
          v-if="countdownTime > 0"
          :time="countdownTime"
          @end="handleCountdownEnd"
          v-slot="{ minutes, seconds, totalMilliseconds }"
        >
          <div class="countdown-container q-mb-lg">
            <div class="countdown-label">
              {{ t('externalPaymentDetailPage.timeRemaining') }}
            </div>
            <div class="countdown-timer">
              {{ String(minutes).padStart(2, '0') }}:{{
                String(seconds).padStart(2, '0')
              }}
            </div>
            <q-linear-progress
              :value="totalMilliseconds / countdownTime"
              color="warning"
              size="10px"
            />
          </div>
        </vue-countdown>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import VueCountdown from '@chenfengyuan/vue-countdown';

interface Props {
  tradeStatus: string;
  countdownTime: number;
  countdownRef: any;
  handleCountdownEnd: () => void;
}

defineProps<Props>();

const { t } = useI18n();
</script>

<style scoped>
.waiting-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #f8f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waiting-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.4s ease;
}

.waiting-card {
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 16px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 4px 20px rgba(37, 4, 83, 0.1);
}

.waiting-title {
  margin-top: 20px;
  font-size: 1.25rem;
  color: #250453;
  font-weight: 600;
}

.waiting-subtitle {
  margin-top: 10px;
  font-size: 0.95rem;
  color: #5209b9;
}

.countdown-container {
  text-align: center;
  padding: 16px;
  border-radius: 12px;
  background-color: #fef7ff;
  border: 1px solid #e5e5e5;
}

.countdown-label {
  font-size: 0.9rem;
  color: #ff9800;
  margin-bottom: 5px;
}

.countdown-timer {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff9800;
  margin-bottom: 5px;
}

/* Dark mode styles */
body.body--dark .waiting-overlay {
  background: #211e36;
}

body.body--dark .waiting-card {
  background: #2b2744;
  border: 1px solid #433a67;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

body.body--dark .waiting-title {
  color: #ffffff;
}

body.body--dark .waiting-subtitle {
  color: #b19ee8;
}

body.body--dark .countdown-container {
  background-color: rgba(125, 63, 250, 0.1);
  border: 1px solid #433a67;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
