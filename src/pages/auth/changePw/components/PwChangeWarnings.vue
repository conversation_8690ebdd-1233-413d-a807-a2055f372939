<template>
  <div v-if="warnings" class="warn-container">
    <div v-if="step === 'addMail'" class="flex items-center">
      <q-icon name="info" class="warning-icon" />
      <div class="warn-content">
        {{ warnings }}
      </div>
    </div>
    <div v-else-if="step === 'sendCodePw'" class="flex items-center">
      <q-icon name="info" class="warning-icon" />
      <div class="warn-content">
        {{ pwCodeMess }}
        <span class="mail_to">{{ auth.loginRes?.email }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { useAuthStore } from 'src/stores';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const q = useQuasar();
const { t } = useI18n();
const auth = useAuthStore();
const isMb = computed(() => q.screen.lt.sm || q.screen.name === 'sm');
const props = defineProps<{
  warnings: string | { mb: string; dk: string };
  step: string;
}>();

const pwCodeMess = computed(() => {
  if (typeof props.warnings === 'string') return props.warnings;
  return isMb.value ? props.warnings.mb : props.warnings.dk;
});
</script>

<style scoped>
.warn-container {
  margin-top: 0.67rem;
  font-family: Inter;
  text-align: center;
}
.warning-icon {
  font-size: 1.375rem;
  margin-right: 0.34375rem;
}
@media (max-width: 768px) {
  .warning-icon {
    font-size: 1rem;
  }
}
.warn-content {
  font-weight: 400;
  font-size: 14px;
}

.mail_to {
  font-weight: 700;
  font-size: 21.5px;
  line-height: 100%;
  letter-spacing: 0%;
}
@media (max-width: 390px) {
  .mail_to {
    font-size: 15.5px;
  }
}
.shine-btn {
  position: relative;
  padding: 0 0 0 6px;
  background: transparent;
  box-shadow: none;
  min-height: unset;
  overflow: visible;
}

.btn-label {
  position: relative;
  display: inline-block;
  font-weight: 600;
  font-size: 16px;
  color: rgba(5, 84, 230, 0.766);
}

.text-shine {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
  color: #007bff;
  transition: width 0.6s ease;
}

.shine-btn:hover .text-shine {
  width: 100%;
}
</style>
