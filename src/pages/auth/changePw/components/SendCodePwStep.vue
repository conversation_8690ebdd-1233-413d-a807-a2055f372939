<template>
  <div class="newPw-container">
    <q-card class="column gap-y-sm q-pa-md">
      <q-form @submit="handleSetPw">
        <!-- New password -->
        <div class="pwAndConfirm-label">
          {{ t('sendCodePwStep.pw_label') }}
        </div>
        <q-input
          class="custom-input"
          outlined
          ref="inputs"
          v-model="newPwd"
          :type="isVisibleSetting ? 'text' : 'password'"
          :rules="passwordRules"
          lazy-rules
          autocomplete="off"
          :placeholder="t('sendCodePwStep.enterPw')"
        >
          <template v-slot:append>
            <q-icon
              :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
              class="cursor-pointer"
              @click="() => (isVisibleSetting = !isVisibleSetting)"
            />
          </template>
        </q-input>

        <!-- confirm password -->
        <div class="pwAndConfirm-label">
          {{ t('sendCodePwStep.pw_confirm_label') }}
        </div>
        <q-input
          class="custom-input"
          outlined
          v-model="confirmPwd"
          :type="isVisibleSetting ? 'text' : 'password'"
          :rules="confirmPasswordRules"
          lazy-rules
          autocomplete="off"
          :placeholder="t('sendCodePwStep.enterPw')"
        >
          <template v-slot:append>
            <q-icon
              :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
              class="cursor-pointer"
              @click="() => (isVisibleSetting = !isVisibleSetting)"
            />
          </template>
        </q-input>
        <q-card class="q-pa-md">
          <div class="text-bold q-mb-sm">
            {{ t('sendCodePwStep.checkMail_pls') }}
          </div>
          <q-input
            v-model="codeSetPw"
            filled
            stack-label
            :label="t('sendCodePwStep.codeLabel')"
            :placeholder="t('sendCodePwStep.placeHolderCode')"
            :rules="[
          (val: string) => /^\d*$/.test(val) || t('sendCodePwStep.codeRule1'),
          (val: string) => !!val || t('sendCodePwStep.codeRule2'),
        ]"
            @keypress="onKeyPress"
          />
        </q-card>
        <q-btn
          type="submit"
          :label="t('sendCodePwStep.pw_submit')"
          :loading="settingPw"
          class="setNew_pw"
        />
      </q-form>
      <q-btn
        :loading="reGetting"
        :uppercase="false"
        flat
        :disable="isCounting"
        :label="
          isCounting
            ? `${t('sendCodePwStep.plseWait')} ${countDown} ${t(
                'sendCodePwStep.second'
              )}`
            : `${t('sendCodePwStep.noCode')}`
        "
        @click="handleRegetCode"
      />
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { QInput } from 'quasar';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../api';
import hooks from 'src/hooks';
import { devEnv } from 'src/router/routes';
import { storageHelper } from 'src/utils/foragePkg';
import { useAuthStore } from 'src/stores';

const { t } = useI18n();
const inputs = ref<QInput>();
const auth = useAuthStore();
const emit = defineEmits(['show-notify']);
// Refs & state
const isVisibleSetting = ref(false);
const newPwd = ref('');
const confirmPwd = ref('');
const codeSetPw = ref('');
// something to wait reget code
const resendWait = devEnv ? 25 : 45;
const countDown = ref(resendWait);
const isCounting = computed(() => countDown.value > 0);
let timer: number | null = null;

onMounted(() => {
  inputs.value?.focus?.();
});

// vue request
// send reset password
const { run: setPass, loading: settingPw } = api.useSetPw({
  onSuccess: async () => {
    await storageHelper<boolean>('isCodeChecked').setItem(true);
    emit('show-notify');
  },
});
// resend to get code
const { run: reGetCode, loading: reGetting } = api.useVfyMail({
  onSuccess: () => {
    hooks.useSuccessNotify(
      `${t('sendCodePwStep.resendOk')} ${auth.loginRes?.email}!!`
    );
    waitToResend();
  },
});
// Validation rules
const passwordRules = [
  (val: string) => !!val || t('sendCodePwStep.enterPw'),
  (val: string) =>
    /^(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/.test(val) ||
    t('sendCodePwStep.pw_rule'),
];

const confirmPasswordRules = [
  (val: string) => !!val || t('sendCodePwStep.enterPw'),
  (val: string) => val === newPwd.value || t('sendCodePwStep.not_match_pw'),
];

const onKeyPress = (event: KeyboardEvent) => {
  const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'];
  if (!/[0-9]/.test(event.key) && !allowedKeys.includes(event.key)) {
    event.preventDefault();
  }
};

const handleSetPw = () => {
  setPass({
    email: auth.loginRes?.email || '',
    newPassword: confirmPwd?.value,
    verificationCode: codeSetPw?.value,
  });
};
// waiting for resend
const waitToResend = () => {
  countDown.value = resendWait;
  timer && clearInterval(timer);
  timer = window.setInterval(() => {
    if (countDown.value <= 1) {
      if (timer !== null) clearInterval(timer);
      timer = null;
      countDown.value = 0;
    } else {
      countDown.value--;
    }
  }, 1000);
};
// set wait
onMounted(() => waitToResend());
const handleRegetCode = () => {
  reGetCode({
    Email: auth.loginRes?.email || '',
  });
};
</script>

<style scoped>
.newPw-container {
  font-family: Inter;
  margin-top: 25.5px;
}
.pwAndConfirm-label {
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
.custom-input {
  margin-top: 10px;
  margin-bottom: 16px;
}
.custom-input:deep(.q-field__control) {
  background-color: rgba(234, 234, 234, 1);
}
body.isDark .custom-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

.custom-input:deep(input::placeholder) {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
.setNew_pw {
  margin-top: 16px;
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}
.setNew_pw:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
</style>
