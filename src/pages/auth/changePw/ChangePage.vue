<template>
  <div class="changePw_container">
    <div class="changePw_tit" v-if="changeStep !== 'done'">
      {{ t('changePage.changePw_tit') }}
    </div>
    <PwChangeWarnings
      v-if="changeStep !== 'done'"
      :warnings="changeWarnings[changeStep]"
      :step="changeStep"
    />
    <AddMailFormStep v-if="changeStep === 'addMail'" @mail-added="addDone" />
    <SendCodePwStep
      v-if="changeStep === 'sendCodePw'"
      :mailSet="addedMail || ''"
      @show-notify="showNotify"
    />
    <PwDoneModal
      v-if="changeStep === 'done'"
      :addedMail="addedMail || ''"
      @remake="resetAll"
      @reLogin="resetAll"
      @countdown="resetAll"
    />
  </div>
</template>

<script setup lang="ts">
import { storageHelper } from 'src/utils/foragePkg';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import AddMailFormStep from './components/AddMailFormStep.vue';
import PwDoneModal from './components/PwDoneModal.vue';
import SendCodePwStep from './components/SendCodePwStep.vue';
import PwChangeWarnings from './components/PwChangeWarnings.vue';

// custom change warning ypes
type ChangeWarningsType = {
  addMail: string;
  sendCodePw: {
    mb: string;
    dk: string;
  };
  done: string;
};

// setup funcs
const { t } = useI18n();
const addedMail = ref<boolean>(false);
const nfyShow = ref<boolean>(false);
const checkedCode = ref<boolean>(false);

const changeWarnings = computed<ChangeWarningsType>(() => ({
  addMail: t('changePage.addMail'),
  sendCodePw: {
    mb: t('changePage.checkMailMb'),
    dk: t('changePage.sendCodePw'),
  },
  done: 'User set ok // 重設密碼成功', // Prompt hidden;'done' controls display!!
}));

const changeStep = computed(() => {
  const addM = addedMail.value;
  const nfy = nfyShow.value;
  const okCode = checkedCode.value;
  if ((addM && nfy) || (addM && okCode)) return 'done';
  if ((addM && !nfy) || (addM && !okCode)) return 'sendCodePw';
  return 'addMail';
});

onMounted(() => {
  addedMail.value = storageHelper<boolean>('mail_vfy').getItem() ?? false;
  checkedCode.value =
    storageHelper<boolean>('isCodeChecked').getItem() ?? false;
});

const addDone = () => {
  addedMail.value = storageHelper<boolean>('mail_vfy').getItem() ?? false;
};

const resetAll = () => {
  addedMail.value = false;
  nfyShow.value = false;
  checkedCode.value = false;
};

const showNotify = () => {
  nfyShow.value = true;
};
</script>

<style scoped>
.changePw_container {
  max-width: 500px;
  margin: 0 auto;
  margin-top: 7.5rem;
  padding: 0 10px;
}

.changePw_tit {
  text-align: start;
  font-family: Roboto;
  font-weight: 700;
  font-size: 1.875rem;
  line-height: 100%;
}

body.isDark .changePw_tit {
  font-family: Inter;
}

@media (max-width: 599px) {
  .changePw_container {
    margin-top: 4rem;
  }
}
</style>
