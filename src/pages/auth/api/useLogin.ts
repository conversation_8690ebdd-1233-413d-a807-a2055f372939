import { useAuthStore } from 'src/stores/useAuthStore';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type LoginProps = {
  email: string;
  password: string;
};

type LoginRes = {
  token: string;
  userId: string;
  email: string;
  phoneNumber: string;
  phoneRegionCode: string;
  roles: Array<RoleK28>;
};

export const useLogin = ({ ...useProps }: UseProps) => {
  const auth = useAuthStore();

  return requestProvider<LoginRes, LoginProps>(
    (props) => {
      const request = axiosProvider
        .post('/auth/login', props)
        .then(({ data }) => data);
      return request;
    },
    {
      manual: true,
      ...useProps,
      onSuccess: async (res) => {
        await auth.setLoginRes(res);
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
};

export type { LoginRes };
