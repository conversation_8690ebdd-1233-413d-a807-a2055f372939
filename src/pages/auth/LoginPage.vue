<template>
  <div class="login_container">
    <q-form class="q-px-none q-px-xs" @submit="handleSubmit">
      <div class="text-start" style="font-weight: 400; font-size: 16px">
        <p
          style="
            font-weight: 700;
            font-size: 33px;
            line-height: 30%;
            letter-spacing: 0%;
          "
        >
          {{ t('loginPage.login') }}
        </p>

        <div class="flex items-center justify-between">
          <div>
            <q-img
              :src="SecLogoLogin"
              alt="sec_logo"
              style="width: 22px; height: 22px; margin-right: 4px"
            />
            <span>
              {{ t('loginPage.sec_confirm') }}
            </span>
          </div>
        </div>
      </div>

      <div>
        <div class="col">
          <div class="input-label">{{ t('loginPage.email') }}</div>
          <q-input
            class="custom-input"
            outlined
            autocomplete="off"
            ref="emailRef"
            v-model="email"
            :rules="[
              (val: string) =>
                !!(val && val.toString().trim()) || t('loginPage.mail_pls'),
              (val: string) =>
                /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ||
                t('loginPage.mail_format'),
            ]"
            inputmode="email"
            :placeholder="t('loginPage.mail_pls')"
          />
        </div>
        <div class="col">
          <div class="input-label">
            {{ t('loginPage.pwd') }}
          </div>
          <q-input
            class="custom-input"
            outlined
            ref="pwdRef"
            v-model="pwd"
            :type="isVisibleSetting ? 'text' : 'password'"
            :rules="[(val: string) => !!val]"
            :error-message="t('loginPage.pwd_pls')"
            autocomplete="off"
            :placeholder="t('loginPage.pwd_pls')"
          >
            <template v-slot:append>
              <q-icon
                :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
                class="cursor-pointer"
                @click="() => (isVisibleSetting = !isVisibleSetting)"
              />
            </template>
          </q-input>
        </div>

        <q-checkbox v-model="rememberMe" :label="t('loginPage.rememberMe')" />

        <div v-if="devEnv" class="q-gutter-sm q-mt-sm">
          <span v-for="(acc, index) in setAccs" :key="index">
            <q-btn
              dense
              size="md"
              :color="acc.color"
              :outline="email !== acc.email || pwd !== acc.pwd"
              @click="quickLogin(acc)"
              class="q-px-sm"
            >
              {{ acc.label }}
            </q-btn>
          </span>
        </div>

        <div class="flex items-center my-2 policyTerms">
          <div>{{ t('loginPage.policyBy') }}</div>
          <q-btn
            v-for="(title, index) in pdfTits"
            :key="index"
            flat
            dense
            no-caps
            @click="openPdf(index)"
            class="link-style"
          >
            {{ title }}
          </q-btn>
        </div>
        <q-btn
          class="full-width q-py-md"
          color="accent"
          type="submit"
          :label="t('loginPage.login_but')"
          :loading="loading"
        />
      </div>
    </q-form>
  </div>

  <PdfViewer
    v-model:showPdf="showPdf"
    :content="currentPdf"
    :title="currentPdfTit"
  />
</template>

<script setup lang="ts">
import SecLogoLogin from 'src/assets/images/SecLogoLogin.png';
import PolicyCN from 'src/assets/pdf/k28DemoPolicyCN.pdf';
import PolicyEng from 'src/assets/pdf/k28DemoPolicyEng.pdf';
import TermsCN from 'src/assets/pdf/k28DemoTermsCN.pdf';
import TermsEng from 'src/assets/pdf/k28DemoTermsEng.pdf';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useLogin } from './api';
import PdfViewer from './components/PdfViewer.vue';
import { devEnv } from 'src/router/routes';

import { forage } from 'src/utils/foragePkg';
const { t, locale } = useI18n();
const router = useRouter();

// form and value
const email = ref('');
const emailRef = ref<HTMLInputElement | null>(null);
const pwd = ref('');
const pwdRef = ref<HTMLInputElement | null>(null);
const isVisibleSetting = ref(false);
const rememberMe = ref(false);

// mutation
const { run: login, loading } = useLogin({
  onSuccess: () => {
    if (rememberMe.value) {
      forage().setItem('loginProps', email.value);
    } else {
      forage().removeItem('loginProps');
    }
    forage().setItem('rememberMe', rememberMe.value);
    router.push({ name: 'home' });
  },
});

// handlers
const handleSubmit = () => {
  login({
    email: email?.value?.toString() as string,
    password: pwd?.value?.toString() as string,
  });
};

// pdf control
const pdfCN = [PolicyCN, TermsCN];
const pdfEng = [PolicyEng, TermsEng];

const pdfTits = computed(() => [
  t('loginPage.policyTit'),
  t('loginPage.termTit'),
]);
const pdfFiles = computed(() => {
  if (locale.value === 'zh-TW') return pdfCN;
  if (locale.value === 'en-US') return pdfEng;
  return pdfCN;
});

// mock account for Agent & notAgent login
const setAccs = computed(() => [
  {
    email: '<EMAIL>',
    pwd: '123456aA',
    label: t('loginPage.member'),
    color: 'purple',
  },
  {
    email: '<EMAIL>',
    pwd: '123456aA',
    label: t('loginPage.agent'),
    color: 'blue',
  },
]);

const quickLogin = (acc: { email: string; pwd: string }) => {
  email.value = acc.email;
  pwd.value = acc.pwd;
};

const currentPdf = ref('');
const currentPdfTit = ref('');
const showPdf = ref(false);

const openPdf = (index: number) => {
  currentPdf.value = pdfFiles.value[index];
  currentPdfTit.value = pdfTits.value[index];
  showPdf.value = true;
};

onMounted(async () => {
  rememberMe.value = (await forage<boolean>().getItem('rememberMe')) || false;
  if (rememberMe.value) {
    email.value = (await forage<string>().getItem('loginProps')) || '';
  }

  if (email.value.trim() === '' && emailRef.value) {
    emailRef.value.focus();
  } else if (pwdRef.value) {
    pwdRef.value.focus();
  }
});
</script>

<style scoped>
.login_container {
  margin-top: 10rem;
}

@media (max-width: 599px) {
  .login_container {
    margin-top: 4rem;
  }
}

.q-form {
  max-width: 489px;
  margin: 0 auto;
  margin-bottom: 20px;
  padding: 0 10px;
}

.link-style {
  cursor: pointer;
  text-decoration: underline;
  color: rgba(2, 86, 240, 1);
}
.link-style:hover {
  text-decoration: underline;
  opacity: 0.8;
}
.input-label {
  margin-bottom: 6px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  margin-top: 16px;
}
.custom-input:deep(.q-field__control) {
  background-color: rgba(234, 234, 234, 1);
}
body.isDark .custom-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

.custom-input:deep(input::placeholder) {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}

.policyTerms {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

.forget_debounce {
  display: flex;
  align-items: center;
  justify-content: center;
}

.forget_debounce .fg_link {
  color: rgba(2, 86, 240, 1);
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}

@media (max-width: 483px) {
  .policyTerms {
    font-size: 10.7px;
  }
  .link-style {
    font-size: 10.7px;
  }
}
</style>
