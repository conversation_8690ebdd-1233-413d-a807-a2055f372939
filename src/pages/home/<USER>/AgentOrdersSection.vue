<template>
  <q-card
    :class="[
      'wrapper full-width',
      isOnDrawer ? 'no-shadow' : q.screen.lt.sm ? 'q-px-sm q-py-lg' : 'q-pa-lg',
    ]"
  >
    <div class="flex items-center justify-between">
      <span
        :class="[
          q.screen.lt.sm ? 'text-h6' : 'text-h5',
          isOnDrawer || q.screen.gt.xs ? '' : 'q-pl-sm',
          'text-bold',
        ]"
        >{{ t('agentOrdersSection.title') }}</span
      >
      <div>
        <q-toggle
          :model-value="!tradeStore.isMute"
          @update:model-value="
            (val: boolean) => (val ? tradeStore.unmuteSound() : tradeStore.muteSound())
          "
          checked-icon="volume_up"
          unchecked-icon="volume_off"
          size="lg"
          color="accent"
        >
          <q-tooltip anchor="bottom middle" self="bottom middle">
            {{ t('agentOrdersSection.sound') }}
          </q-tooltip>
        </q-toggle>

        <q-toggle
          disable
          v-model="autoAccept"
          checked-icon="check_circle"
          unchecked-icon="remove_circle_outline"
          size="lg"
          color="accent"
        >
          <q-tooltip anchor="bottom middle" self="bottom middle">
            {{ t('agentOrdersSection.autoAccept') }}
          </q-tooltip>
        </q-toggle>
      </div>
    </div>

    <q-tabs
      v-model="tabs"
      active-color="accent"
      indicator-color="accent"
      narrow-indicator
      align="left"
      class="text-gray"
    >
      <q-tab name="real-time" :label="t('agentOrdersSection.realTimeOrders')">
        <q-badge
          v-if="pendingOrders?.items.length"
          color="red"
          :label="pendingOrders?.totalCount"
          floating
        />
      </q-tab>
      <q-tab
        name="in-progress"
        :label="t('agentOrdersSection.inProgressOrders')"
      >
        <q-badge
          v-if="inProgressOrders?.items.length"
          color="red"
          :label="inProgressOrders?.totalCount"
          floating
        />
      </q-tab>
    </q-tabs>
    <q-separator />

    <q-tab-panels
      v-model="tabs"
      animated
      class="q-pt-sm"
      style="border-radius: 0"
    >
      <q-tab-panel name="real-time" class="q-pa-none">
        <LoadingComponent v-if="loadingPendingOrders" />
        <EmptyMaster
          v-else-if="!pendingOrders || pendingOrders?.items.length === 0"
          :message="t('agentOrdersSection.empty')"
          icon="data_object"
        />
        <div v-else>
          <PaginationComponent
            v-model="currentRealTimePage"
            :page-size="realTimePageSize"
            :total-pages="pendingOrders.totalPages"
            :total-count="pendingOrders.totalCount"
            @update:modelValue="setRealTimePage"
            @update:pageSize="setRealTimePageSize"
          />
          <RealTimePending :pending="pendingOrders" />
        </div>
      </q-tab-panel>
      <q-tab-panel name="in-progress" class="q-pa-none">
        <LoadingComponent v-if="loadingInProgressOrders" />
        <EmptyMaster
          v-else-if="!inProgressOrders || inProgressOrders?.items.length === 0"
          :message="t('agentOrdersSection.empty')"
          icon="data_object"
        />
        <div v-else>
          <PaginationComponent
            v-model="currentInProgressPage"
            :page-size="inProgressPageSize"
            :total-pages="inProgressOrders.totalPages"
            :total-count="inProgressOrders.totalCount"
            @update:modelValue="setInProgressPage"
            @update:pageSize="setInProgressPageSize"
          />
          <InProgressOrders :inProgress="inProgressOrders" />
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </q-card>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { useAuthStore, useTradeStore } from 'src/stores';
import { provide, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../api';
import InProgressOrders from './InProgressOrders.vue';
import RealTimePending from './RealTimePending.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import EmptyMaster from 'src/components/EmptyMaster.vue';
import usePagination from 'src/hooks/usePagination';
import PaginationComponent from 'src/components/PaginationComponent.vue';
import { TradeStatusEnum, TradeTypeEnum } from 'src/utils/enums';

const tradeStore = useTradeStore();
const authStore = useAuthStore();
const q = useQuasar();
const { t } = useI18n();
const tabs = ref<'real-time' | 'in-progress'>('real-time');
const triggerReload = ref(false);

const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>();
const autoAccept = ref(false);
const {
  currentPage: currentInProgressPage,
  pageSize: inProgressPageSize,
  setPage: setInProgressPage,
  setPageSize: setInProgressPageSize,
} = usePagination();
const {
  currentPage: currentRealTimePage,
  pageSize: realTimePageSize,
  setPage: setRealTimePage,
  setPageSize: setRealTimePageSize,
} = usePagination();

// vue request
// pending Order
const {
  data: pendingOrders,
  loading: loadingPendingOrders,
  run: getPendingOrders,
  refresh: refreshPendingOrders,
} = api.usePendingOrder({
  pageNumber: currentRealTimePage.value,
  pageSize: realTimePageSize.value,
});

// inProgress Order
const {
  data: inProgressOrders,
  loading: loadingInProgressOrders,
  run: getInProgressOrders,
  refresh: refeshInProgressOrders,
} = api.useOrderList({
  pageNumber: currentInProgressPage.value,
  pageSize: inProgressPageSize.value,
  StatusGroup: 'Ongoing',
});

const { data: isPaidOrders, refresh: refreshIsPaidOrders } = api.useOrderList({
  type: TradeTypeEnum.Buy,
  Statuses: [TradeStatusEnum.PaymentMade],
});

provide('triggerReload', triggerReload);
provide('loadingPendingOrders', loadingPendingOrders);

// refresh pending orders if agent accept an expires order
watch(triggerReload, () => {
  refreshPendingOrders();
});
// watch store to update data
watch(
  () => tradeStore.trade,
  (newTrade) => {
    if (!newTrade) return;

    if (newTrade.status === TradeStatusEnum.Accepted) {
      refreshPendingOrders();
      refeshInProgressOrders();
      tradeStore.playProgressSound();
    } else if (
      [TradeStatusEnum.Pending, TradeStatusEnum.Expired].includes(
        newTrade.status
      )
    ) {
      refreshPendingOrders();
    } else if (
      [
        TradeStatusEnum.BankAccountAssigned,
        TradeStatusEnum.PaymentMade,
        TradeStatusEnum.PaymentConfirmed,
        TradeStatusEnum.Completed,
        TradeStatusEnum.Cancelled,
      ].includes(newTrade.status)
    ) {
      refeshInProgressOrders();
      tradeStore.playProgressSound();

      if (
        [
          TradeStatusEnum.PaymentMade,
          TradeStatusEnum.PaymentConfirmed,
          TradeStatusEnum.Completed,
          TradeStatusEnum.Cancelled,
        ].includes(newTrade.status)
      ) {
        refreshIsPaidOrders();
      }
    }
  }
);

watch(pendingOrders, (newPendingOrders) => {
  if (newPendingOrders?.totalCount && authStore.isAgent) {
    tradeStore.playPendingSound();
  } else {
    tradeStore.stopPendingSound();
  }

  if (!newPendingOrders) return;

  tradeStore.setTotalPending(newPendingOrders.totalCount);
});
watch(inProgressOrders, (newInProgressOrders) => {
  if (!newInProgressOrders) return;

  tradeStore.setTotalInProgress(newInProgressOrders.totalCount);
});
watch(isPaidOrders, (newIsPaidOrders) => {
  if (newIsPaidOrders?.totalCount && authStore.isAgent) {
    tradeStore.playPaymentSound();
  } else {
    tradeStore.stopPaymentSound();
  }
});

watch([currentInProgressPage, inProgressPageSize], () => {
  getInProgressOrders({
    pageNumber: currentInProgressPage.value,
    pageSize: inProgressPageSize.value,
    StatusGroup: 'Ongoing',
  });
});
watch([currentRealTimePage, realTimePageSize], () => {
  getPendingOrders({
    pageNumber: currentRealTimePage.value,
    pageSize: realTimePageSize.value,
  });
});
</script>

<style scoped>
.wrapper {
  border-radius: 24px;
}
</style>
