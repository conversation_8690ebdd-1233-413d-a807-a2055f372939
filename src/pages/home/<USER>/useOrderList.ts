import { Trade } from 'src/pages/trade/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import { TradeModeEnum, TradeStatusEnum, TradeTypeEnum } from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

type OrderListProps = {
  pageNumber?: number | null;
  pageSize?: number | null;
  orderBy?: 'Id' | null;
  StartTime?: string;
  EndTime?: string;
  orderByDescending?: boolean | null;
  StatusGroup?: 'Ongoing' | 'Completed' | 'Failed';
  mode?: TradeModeEnum;
  type?: TradeTypeEnum;
  Statuses?: Array<TradeStatusEnum>;
};

interface OrderListRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<Trade>;
}

const useOrderList = (useProps: OrderListProps) => {
  const vueRequest = requestProvider<OrderListRes, OrderListProps>(
    (props) => {
      const statusQuery = props.Statuses?.map(
        (status) => `statuses=${status}`
      ).join('&');
      const request = axiosProvider
        .get(`/trade-orders/my${statusQuery ? '?' + statusQuery : ''}`, {
          params: { ...props, Statuses: undefined },
        })
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: false,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export default useOrderList;
export type { OrderListProps, OrderListRes };
