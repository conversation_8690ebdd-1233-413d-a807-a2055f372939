<template>
  <div :class="['wrapper q-py-md', q.screen.lt.sm ? 'q-px-md' : 'q-px-lg']">
    <section class="flex items-start justify-between">
      <div
        :class="[
          'flex items-center gap-x-sm',
          isBuy ? 'text-info' : 'text-negative',
        ]"
      >
        <span class="text-h5 text-bold">{{
          isBuy ? t('pendingItem.buy') : t('pendingItem.sell')
        }}</span>
        <span
          class="text-white q-px-sm rounded-borders text-caption"
          :class="isBuy ? 'bg-info' : 'bg-negative'"
          >{{ order.cryptoSymbol }}/{{ order.fiatCurrency }}</span
        >
        <span v-if="devEnv">{{ order.id }}</span>
      </div>

      <div class="flex q-gutter-x-md">
        <div class="column items-end text-bold">
          <span class="amount">{{
            thousandTool(order.fiatAmount, order.fiatCurrency as DigitTypes)
          }}</span>
          <span class="currency">{{ order.fiatCurrency }}</span>
        </div>
        <div
          :class="[
            'column items-end text-bold',
            isBuy ? 'text-info' : 'text-negative',
          ]"
        >
          <span class="amount">{{
            thousandTool(order.cryptoAmount, order.cryptoSymbol as DigitTypes)
          }}</span>
          <span class="currency">{{ order.cryptoSymbol }}</span>
        </div>
      </div>
    </section>
    <section class="flex justify-between gap-x-lg">
      <div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('pendingItem.orderNumber') }}:</span>
          <span class="text-bold text-grey">{{ order.tradeNo }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('pendingItem.createdAt') }}:</span>
          <span class="text-bold">{{
            dayjs(order.createdAt).format(dateFormator.accurate)
          }}</span>
        </div>
      </div>

      <section class="column" :class="q.screen.lt.sm ? '' : 'items-end'">
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('pendingItem.exchangeRate') }}:</span>
          <span class="text-bold text-grey"> {{ order.rate }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span
            >{{
              isBuy ? t('pendingItem.seller') : t('pendingItem.buyer')
            }}:</span
          >
          <span v-if="isBuy" class="text-bold text-grey">{{
            order.accountName || '--'
          }}</span>
          <span v-else class="text-bold text-grey">{{
            order.payerBankAccountName || '--'
          }}</span>
        </div>
      </section>
    </section>

    <section class="flex items-center justify-between">
      <div v-if="isPending" class="flex gap-x-xs text-orange text-bold">
        <q-icon name="info" size="1.2rem" />
        <span>{{ t('pendingItem.expiresIn') }}:</span>
        <countdown-timer
          :startAt="order.updatedAt"
          :duration="PENDING_TIME_LIMIT"
        />
      </div>
      <q-btn
        @click="!isBuy ? handleAcceptBuy(order) : handleAcceptSell(order)"
        unelevated
        outline
        color="accent"
        padding="0.25rem 1rem"
        class="btn-accept"
        :label="t('pendingItem.btnAccept')"
      />
    </section>
  </div>

  <q-dialog v-model="consentProblem" persistent>
    <q-card class="noBank-card q-pa-md" style="min-width: 300px">
      <q-card-section class="text-h6">
        {{ modalTitle }}
      </q-card-section>

      <q-card-section>
        {{ modalMessage }}
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          label="Cancel"
          color="grey"
          @click="consentProblem = false"
        />
        <q-btn
          v-if="isNoBank"
          flat
          label="Add Bank"
          color="primary"
          @click="() => router.push({ name: 'accounts' })"
        />
        <q-btn
          v-if="isNotEnough"
          flat
          label="Go Wallet"
          color="primary"
          @click="() => router.push({ name: 'wallets' })"
        />
        <q-btn
          :loading="loadingPendingOrders"
          v-if="isOrdTimeOut"
          flat
          label="Update Order"
          color="primary"
          @click="
            () => {
              triggerReload = !triggerReload;
            }
          "
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, inject, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { AxiosError } from 'axios';
import dayjs from 'dayjs';
import { useQuasar } from 'quasar';
import useSwapTradeType from 'src/hooks/useSwapTradeType';
import { devEnv } from 'src/router/routes';
import { PENDING_TIME_LIMIT } from 'src/utils/constants';
import {
  TradeTypeEnum,
  TradeStatusEnum,
  OrderAcceptProblem,
} from 'src/utils/enums';
import { dateFormator } from 'src/utils/dateTool';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { ErrorRes } from 'src/utils/requestProvider';
import api from '../api';
import { OrderPending } from '../api/usePendingOrder';
import CountdownTimer from 'src/components/CountdownTimer.vue';

const router = useRouter();
const { order } = defineProps<{ order: OrderPending }>();
const q = useQuasar();
const { t } = useI18n();

const consentProblem = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');
const isNoBank = ref(false);
const isNotEnough = ref(false);
const isOrdTimeOut = ref(false);
const triggerReload = inject('triggerReload') as Ref<boolean>;
const loadingPendingOrders = inject('loadingPendingOrders') as Ref<boolean>;

const { run: acceptBuy } = api.useAcceptBuyOrder({
  onError: (err) => {
    const errCode = (err as AxiosError<ErrorRes>).response?.data.errorCode;
    if (errCode) handleConsentOrd(errCode);
  },
});

const { run: acceptSell } = api.useAcceptSellOrder({
  onError: (err) => {
    const errCode = (err as AxiosError<ErrorRes>).response?.data.errorCode;
    if (errCode) handleConsentOrd(errCode);
  },
});

const isBuy = computed(
  () => useSwapTradeType(order.type) === TradeTypeEnum.Buy
);
const isPending = computed(() => order.status === TradeStatusEnum.Pending);

const handleAcceptBuy = (pending: OrderPending) => {
  acceptBuy({ id: pending.id });
};
const handleAcceptSell = (pending: OrderPending) => {
  acceptSell({ id: pending.id });
};

const handleConsentOrd = (err: number) => {
  isNoBank.value = false;
  isNotEnough.value = false;
  isOrdTimeOut.value = false;
  consentProblem.value = true;

  const errorMessages: Record<number, { title: string; message: string }> = {
    [OrderAcceptProblem.noBank]: {
      title: t('pendingItem.noBankTit'),
      message: t('pendingItem.messNoBank'),
    },
    [OrderAcceptProblem.notEnough]: {
      title: t('pendingItem.notEnoughTit'),
      message: t('pendingItem.messNotEnough'),
    },
    [OrderAcceptProblem.timeOut]: {
      title: t('pendingItem.timeOutTit'),
      message: t('pendingItem.messTimeOut'),
    },
  };

  const error = errorMessages[err];

  if (error) {
    modalTitle.value = error.title;
    modalMessage.value = error.message;
  } else {
    console.warn('Check Da Order Error', err);
  }

  switch (err) {
    case OrderAcceptProblem.noBank:
      isNoBank.value = true;
      break;
    case OrderAcceptProblem.notEnough:
      isNotEnough.value = true;
      break;
    case OrderAcceptProblem.timeOut:
      isOrdTimeOut.value = true;
      break;
  }
};
</script>

<style scoped>
.wrapper {
  border-radius: 6px;
  border: 1px solid var(--border-color);
  margin-top: 10px;
}

.exchange-rate {
  border: 1px solid currentColor;
  font-size: 0.75rem;
}

.amount {
  font-size: 1rem;
}

.currency {
  font-size: 0.75rem;
  margin-top: -0.25rem;
}

.status-tag {
  border-radius: 2px;
}

.btn-accept {
  width: fit-content;
}
</style>
