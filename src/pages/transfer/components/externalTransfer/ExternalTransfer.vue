<template>
  <q-stepper
    v-model="stepTransfer"
    flat
    animated
    alternative-labels
    header-class="no-border no-wrap"
    done-color="accent"
    active-color="accent"
  >
    <q-step
      :name="1"
      :title="t('externalTransfer.step1Tit')"
      :done="stepTransfer > 1"
      icon="assignment"
    >
      <TransferCodeStep
        :form="form"
        @open-scanner="openScanner = true"
        @update:form="handleCodeTransfer"
        :codeLoad="codeLoad"
      />
    </q-step>

    <q-step
      :name="2"
      :title="t('externalTransfer.step2Tit')"
      icon="account_balance_wallet"
    >
      <TransferStep
        :form="form"
        @editInfo="backToEdit"
        @update:codeTransfer="handleSubmit"
        @resendTransferCode="handleCodeTransfer"
        :reCodeLoad="codeLoad"
      />
    </q-step>
  </q-stepper>
  <TransferConfirmModal
    mode="external"
    v-model="showConfirmModal"
    :network="form.network.value"
    :address="form.address"
    :codeTransfer="form.codeTransfer || ''"
    :note="form.note"
    :amount="numberTool(form.amount)"
    @confirm="handleExTransfer"
    :transferLoad="exTransLoad"
  />
</template>

<script setup lang="ts">
import { useAuthStore, useWalletsStore } from 'src/stores';
import { forage } from 'src/utils/foragePkg';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../../api';
import { walletNetworkOptions } from 'src/utils/options';
import hooks from 'src/hooks';
import { numberTool } from 'src/utils/NumberTool';
import TransferCodeStep from './TransferCodeStep.vue';
import TransferStep from './TransferStep.vue';
import TransferConfirmModal from '../TransferConfirmModal.vue';
import { ExTransferFormType } from './types';

const { t } = useI18n();
const auth = useAuthStore();
const walletsStore = useWalletsStore();
const transferDetail = forage();
const stepTransfer = ref(1);
const openScanner = ref(false);
const showConfirmModal = ref(false);
const transferRequest = ref<ExTransferFormType | null>(null);

// vue request
// send code to mail
const { run: getCode, loading: codeLoad } = api.useCodeTransfer({
  onSuccess: async () => {
    await transferDetail.setItem(
      'sentCodeTransfer',
      auth.loginRes?.email || ''
    );
    if (transferRequest.value) {
      await transferDetail.setItem('transferRequest', transferRequest.value);
    }
    showConfirmModal.value = false;
    hooks.useSuccessNotify(
      `${t('externalTransfer.plsCheckMail')} ${auth.loginRes?.email} ${t(
        'externalTransfer.toGetCode'
      )}`
    );
    stepTransfer.value = 2;
  },
});
// transfer
const { run: transfer, loading: exTransLoad } = api.useExTransfer({
  onSuccess: async () => {
    await transferDetail.removeItem('sentCodeTransfer');
    await transferDetail.removeItem('transferRequest');
    Object.assign(form, {
      network: walletNetworkOptions.map((option) => ({
        ...option,
        label: t('options.' + option.label),
      }))[0],
      address: '',
      amount: '0',
      agreement: false,
      note: '',
    });
    hooks.useSuccessNotify(`${t('externalTransfer.transferSuccess')}`);
    stepTransfer.value = 1;
    walletsStore.triggerUpdate();
  },
});
const form = reactive<ExTransferFormType>({
  network: walletNetworkOptions.map((option) => ({
    ...option,
    label: t('options.' + option.label || 'undefined'),
  }))[0],
  address: '',
  amount: '0',
  agreement: false,
  codeTransfer: '',
  note: '',
});

const handleSubmit = (code: string) => {
  form.codeTransfer = code;
  showConfirmModal.value = true;
};

const handleCodeTransfer = (val: ExTransferFormType) => {
  Object.assign(form, val);
  transferRequest.value = { ...val };
  const payload = {
    amount: numberTool(val.amount),
    to: val.address,
  };

  getCode(payload);
};

const handleExTransfer = () => {
  transfer({
    amount: numberTool(form.amount),
    to: form.address,
    code: form.codeTransfer,
    note: form.note,
  });
};

// handle set step
const backToEdit = async () => {
  await transferDetail.removeItem('sentCodeTransfer');
  stepTransfer.value = 1;
};
// check da transfer step
onMounted(async () => {
  const sentMail = await transferDetail.getItem('sentCodeTransfer');
  if (sentMail === auth.loginRes?.email) {
    stepTransfer.value = 2;
  }
  const savedRequest = await forage<ExTransferFormType>().getItem(
    'transferRequest'
  );
  if (savedRequest) {
    Object.assign(form, savedRequest);
  }
});
</script>

<style scoped></style>
