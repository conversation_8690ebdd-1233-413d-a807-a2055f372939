<template>
  <q-page padding class="container q-py-lg flex items-center column">
    <breadcrumb-component
      :label="t('transferPage.title')"
      class="self-start q-mb-lg"
    />

    <q-card :class="['full-width', q.screen.lt.sm ? 'q-pa-md' : 'q-pa-lg']">
      <q-tabs
        v-model="tabs"
        align="justify"
        active-color="accent"
        indicator-color="accent"
        inline-label
        mobile-arrows
      >
        <q-tab name="external">
          {{ t('transferPage.exTransTab') }}
        </q-tab>

        <q-tab name="internal">
          {{ t('transferPage.inTransTab') }}
        </q-tab>
      </q-tabs>
      <q-separator />

      <q-tab-panels v-model="tabs" animated>
        <q-tab-panel name="external"> <ExternalTransfer /> </q-tab-panel>
        <q-tab-panel name="internal">
          <InternalTransfer />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import BreadcrumbComponent from 'src/components/BreadcrumbComponent.vue';
import { Ref, ref } from 'vue';
import ExternalTransfer from './components/externalTransfer/ExternalTransfer.vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import InternalTransfer from './components/internalTransfer/InternalTransfer.vue';

const tabs: Ref<'external' | 'internal'> = ref('external');
const { t } = useI18n();
const q = useQuasar();
</script>

<style scoped></style>
