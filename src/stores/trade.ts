import { defineStore } from 'pinia';
import { Trade } from 'src/pages/trade/api';
import pendingSoundFile from 'src/assets/sounds/pending.mp3';
import matchSoundFile from 'src/assets/sounds/match.mp3';
import paymentSoundFile from 'src/assets/sounds/payment.mp3';

export const useTradeStore = defineStore('trade', {
  state: (): {
    trade: Trade | null;
    isMute: boolean;
    pendingSound: HTMLAudioElement;
    progressSound: HTMLAudioElement;
    paymentSound: HTMLAudioElement;
    totalPending: number;
    totalInProgress: number;
  } => ({
    trade: null,
    isMute: false,
    pendingSound: (() => {
      const audio = new Audio(pendingSoundFile);
      audio.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
      audio.loop = true;
      return audio;
    })(),
    progressSound: (() => {
      const audio = new Audio(matchSoundFile);
      audio.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
      return audio;
    })(),
    paymentSound: (() => {
      const audio = new Audio(paymentSoundFile);
      audio.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
      audio.loop = true;
      return audio;
    })(),
    totalPending: 0,
    totalInProgress: 0,
  }),
  actions: {
    setTrade(data: Trade) {
      this.trade = data;
    },
    muteSound() {
      this.isMute = true;
      this.pendingSound.volume = 0;
      this.progressSound.volume = 0;
      this.paymentSound.volume = 0;
    },
    unmuteSound() {
      this.isMute = false;
      this.pendingSound.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
      this.progressSound.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
      this.paymentSound.volume = import.meta.env.VITE_DEFAULT_VOLUME || 1;
    },
    stopPendingSound() {
      this.pendingSound.pause();
      this.pendingSound.currentTime = 0;
    },
    stopProgressSound() {
      this.progressSound.pause();
      this.progressSound.currentTime = 0;
    },
    stopPaymentSound() {
      this.paymentSound.pause();
      this.paymentSound.currentTime = 0;
    },
    playPendingSound() {
      this.pendingSound.play();
    },
    playProgressSound() {
      this.progressSound.play();
    },
    playPaymentSound() {
      this.paymentSound.play();
    },
    setTotalPending(data: number) {
      this.totalPending = data;
    },
    setTotalInProgress(data: number) {
      this.totalInProgress = data;
    },
    reset(callback?: () => void) {
      this.trade = null;
      this.isMute = false;
      this.stopPendingSound();
      this.stopProgressSound();
      this.stopPaymentSound();
      this.totalPending = 0;
      this.totalInProgress = 0;
      if (callback) callback();
    },
  },
});
