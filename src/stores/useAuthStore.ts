import { defineStore } from 'pinia';
import { type LoginRes } from 'src/pages/auth/api/useLogin';
import { forage } from 'src/utils/foragePkg';
import { ref } from 'vue';
import type { ForageCallback } from 'src/utils/foragePkg';

export const useAuthStore = defineStore('auth', {
  state: () => {
    const loginRes = ref<LoginRes>();
    const proxyToken = ref<string>();
    const isInitialized = ref({ loginRes: false, proxyToken: false });
    forage<LoginRes>()
      .getItem('loginRes', (_, value) => {
        if (value) loginRes.value = value;
      })
      .finally(() => {
        isInitialized.value.loginRes = true;
      });
    forage<string>()
      .getItem('proxyToken', (_, value) => {
        if (value) proxyToken.value = value;
      })
      .finally(() => {
        isInitialized.value.proxyToken = true;
      });
    return {
      isInitialized,
      loginRes,
      proxyToken,
    };
  },
  getters: {
    isLoggedIn: (state) => !!state.loginRes?.token,
    isAgent: (state) => state.loginRes?.roles?.includes('agent'),
  },
  actions: {
    async setLoginRes(data: LoginRes) {
      this.loginRes = data;
      await forage<LoginRes>().setItem('loginRes', data);
      forage<string>().removeItem('proxyToken');
    },
    async setProxyToken(token: string) {
      this.proxyToken = token;
      await forage<string>().setItem('proxyToken', token);
      forage<string>().removeItem('loginRes');
    },
    reset(callback?: ForageCallback<LoginRes>) {
      this.loginRes = undefined;
      this.proxyToken = undefined;
      if (callback) callback();
    },
  },
});
