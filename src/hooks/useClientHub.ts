import * as signalR from '@microsoft/signalr';
import { useLogout } from 'src/api';
import { LoginRes } from 'src/pages/auth/api/useLogin';
import { Message, Trade, useGetTradeById } from 'src/pages/trade/api';
import { Wallet } from 'src/pages/wallets/api';
// prettier-ignore
import { useAuthStore, useChatStore, useTradeStore, useWalletsStore } from 'src/stores';
import { ChatMessageType } from 'src/utils/enums';
import { forage } from 'src/utils/foragePkg';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { reactive } from 'vue-demi';
import { useRoute, useRouter } from 'vue-router';

const url = import.meta.env.VITE_CLIENT_HUBS;
const isDev = import.meta.env.DEV;

export function useClientHub(manuallyConnect = false) {
  const route = useRoute();
  const router = useRouter();
  const auth = useAuthStore();
  const chatStore = useChatStore();
  const tradeStore = useTradeStore();
  const walletsStore = useWalletsStore();

  // States
  const connection = ref<signalR.HubConnection | null>(null);
  const isConnected = ref(false);

  // Request
  const { data: trade, run: getTradeById } = useGetTradeById();
  const { run: logout } = useLogout(
    reactive({
      config: {
        onSuccess: () => {
          router.push({ name: 'login' });
        },
      },
    })
  );

  const startConnection = async (proxyToken?: string | null | undefined) => {
    const loginRes = await forage<LoginRes>().getItem('loginRes');
    const token = proxyToken || loginRes?.token;

    if (!token) {
      console.warn('Token is required to connect to client hub.');
      return;
    }

    const hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(url, {
        accessTokenFactory: () => token,
      })
      .withAutomaticReconnect()
      .configureLogging(signalR.LogLevel.Information)
      .build();

    connection.value = hubConnection;

    try {
      await hubConnection.start();
      isConnected.value = true;
      if (isDev) console.log('Connected to client hub');

      // add new events here
      hubConnection.on('BalanceUpdated', (data: Array<Wallet>) => {
        walletsStore.setWallets(data);
      });
      hubConnection.on('TradeUpdated', (data: Trade) => {
        tradeStore.setTrade(data);
      });
      hubConnection.on('ReceiveChatMessage', (data: Message) => {
        chatStore.setMessage(data, route.query.id === data.tradeId.toString());
        // find an in-progress order only if message is sent from another user
        if (data.senderId !== auth.loginRes?.userId)
          getTradeById({ id: data.tradeId });
      });
    } catch (err) {
      console.error(
        'Connection failed:',
        err,
        err instanceof signalR.HttpError
      );
      if (err instanceof Error) {
        if (err.message.includes('401')) {
          logout({});
        }
      }
      isConnected.value = false;
    }
  };

  chatStore.setSendMessage(
    async (payload: {
      tradeId: number;
      payload: string;
      messageType?: ChatMessageType;
      caption?: string;
      file?: File | null;
    }) => {
      if (
        !connection.value ||
        connection.value.state !== signalR.HubConnectionState.Connected
      ) {
        console.warn('Not connected to hub.');
        return;
      }

      try {
        const {
          tradeId,
          payload: content,
          messageType = ChatMessageType.Text,
          caption,
        } = payload;

        const formData = new FormData();
        formData.append('TradeId', tradeId.toString());
        formData.append('Payload', content);
        formData.append('MessageType', messageType.toString());

        await connection.value.invoke('SendTradeChatMessage', {
          tradeId,
          payload: content,
          messageType,
          caption,
        });
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  );

  onMounted(() => {
    if (manuallyConnect) return;
    startConnection();
  });

  onUnmounted(() => {
    if (connection.value) {
      connection.value.stop();
      if (isDev) console.log('Disconnected from client hub');
    }
  });

  watch(trade, () => {
    if (trade.value && trade.value?.items.length > 0 && chatStore.audio) {
      chatStore.audio.play();
    }
  });

  return {
    connection,
    isConnected,
    startConnection,
  };
}
