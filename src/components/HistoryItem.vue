<template>
  <q-item
    clickable
    class="wrapper q-py-md column gap-y-xs"
    @click="$emit('open-details-modal', history)"
  >
    <section class="flex items-start justify-between no-wrap">
      <div
        :class="[
          'flex items-center gap-x-sm',
          isTransfer ? 'text-accent' : isBuy ? 'text-info' : 'text-negative',
        ]"
      >
        <span class="text-h5 text-bold">{{ flowTypeName }}</span>
        <span
          v-if="!isTransfer"
          class="text-white q-px-sm rounded-borders text-caption"
          :class="
            isTransfer ? 'text-accent' : isBuy ? 'text-info' : 'text-negative'
          "
          >{{ history.cryptoSymbol }}/{{ history.fiatSymbol }}</span
        >
      </div>

      <div class="flex gap-x-md no-wrap">
        <div v-if="!isTransfer" class="column items-end text-bold">
          <span class="amount">{{
            thousandTool(history.fiatAmount, history.fiatSymbol)
          }}</span>

          <span class="currency">{{ history.fiatSymbol }}</span>
        </div>
        <div
          :class="[
            'column items-end text-bold',
            isTransfer ? 'text-accent' : isBuy ? 'text-info' : 'text-negative',
          ]"
        >
          <span class="amount"
            >{{ isBuy || isDeposit ? '+' : ''
            }}{{
              thousandTool(history.changeAmount, history.cryptoSymbol)
            }}</span
          >

          <span class="currency"> {{ history.cryptoSymbol }}</span>
        </div>
      </div>
    </section>

    <section class="flex items-end justify-between gap-x-xl">
      <div>
        <div v-if="isTrade" class="flex gap-x-xs text-grey">
          <span>{{ t('historyItem.orderNumber') }}:</span>
          <span class="text-bold text-grey">{{ history.sourceId }}</span>
        </div>
        <!-- <div v-else-if="isBalanceTransfer" class="flex gap-x-xs text-grey"> -->
        <!-- <span -->
        <!--   >{{-->
        <!--     isDeposit ? t('historyItem.from') : t('historyItem.to')-->
        <!--   }}:</span-->
        <!-- > -->
        <!-- <span class="text-bold text-grey">{{-->
        <!--   history.counterpartyName-->
        <!-- }}</span>-->
        <!-- </div> -->
        <div v-if="isExternalWithdraw" class="flex gap-x-xs text-grey">
          <span>{{ t('historyItem.fee') }}:</span>
          <span class="text-bold text-grey">
            {{ thousandTool(2, history.cryptoSymbol) }}
            {{ history.cryptoSymbol }}
          </span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('historyItem.completedAt') }}:</span>
          <span class="text-bold text-grey">
            {{
              history.timestamp
                ? dayjs(history.timestamp).format(dateFormator.accurate)
                : '--'
            }}
          </span>
        </div>
      </div>

      <div class="column" :class="q.screen.lt.sm ? '' : 'items-end'">
        <template v-if="isTrade">
          <div class="flex gap-x-xs text-grey">
            <span
              >{{
                isBuy
                  ? t('historyItem.sellerName')
                  : t('historyItem.buyerName')
              }}:</span
            >
            <span class="text-bold text-grey">{{
              history.counterpartyName
            }}</span>
          </div>
        </template>
        <div v-if="!isTrade" class="flex gap-x-xs text-grey">
          <span>{{ t('historyItem.note') }}:</span>
          <span class="text-bold text-grey">{{ history.note || '--' }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('historyItem.balance') }}:</span>
          <span class="text-bold text-grey"
            >{{ thousandTool(history.balanceAfter, history.cryptoSymbol) }}
            {{ history.cryptoSymbol }}</span
          >
        </div>
      </div>
    </section>
  </q-item>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import { dateFormator } from 'src/utils/dateTool';
import { FlowTypeEnum, LedgerSourceTypeEnum } from 'src/utils/enums';
import { thousandTool } from 'src/utils/NumberTool';
import { flowTypeEnumOptions } from 'src/utils/options';
import { History } from 'src/pages/history/api';

const { history } = defineProps<{
  history: History;
}>();

const q = useQuasar();
const { t } = useI18n();

const flowTypeName = computed(() =>
  t(
    `options.${
      flowTypeEnumOptions.find(
        (flowType) => flowType.value === history.flowType
      )?.label
    }` || ''
  )
);
const isBuy = computed(() => history.flowType === FlowTypeEnum.Buy);
const isDeposit = computed(() => history.flowType === FlowTypeEnum.Deposit);
const isTrade = computed(
  () => history.ledgerSourceType === LedgerSourceTypeEnum.Trade
);
const isExternalWithdraw = computed(
  () =>
    history.ledgerSourceType === LedgerSourceTypeEnum.ChainTxLog &&
    history.flowType === FlowTypeEnum.Withdrawal
);
const isTransfer = computed(
  () =>
    history.flowType === FlowTypeEnum.Deposit ||
    history.flowType === FlowTypeEnum.Withdrawal
);
</script>

<style scoped>
.wrapper {
  border-bottom: 1px solid var(--border-color);
  border-radius: 0px;
}

.amount {
  font-size: 1rem;
}

.currency {
  font-size: 0.75rem;
  margin-top: -0.25rem;
}
</style>
