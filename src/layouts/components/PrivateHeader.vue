<template>
  <q-header
    :elevated="themes.isDark ? false : true"
    class="header bg-gradient-tr"
  >
    <q-toolbar class="toolbar">
      <q-toolbar-title>
        <router-link
          :to="{ name: 'home' }"
          class="q-pa-none flex items-center gap-x-md"
        >
          <q-img
            v-if="q.screen.gt.xs"
            no-spinner
            no-transition
            fetchpriority="high"
            :src="PvLogoFull"
            class="desktop-logo"
          />
          <q-img
            v-else
            loading="eager"
            no-spinner
            no-transition
            fetchpriority="high"
            :src="PvLogoMb"
            class="mobile-logo"
          />
          <div v-if="isDev" class="text-bold text-white">DEV</div>
        </router-link>
      </q-toolbar-title>

      <div class="flex-center q-gutter-x-sm">
        <DropDownAsset />
        <q-btn
          v-if="route.name !== 'home'"
          flat
          round
          icon="receipt_long"
          @click="privateNfyOpen"
        >
          <q-badge
            v-if="tradeStore.totalPending || tradeStore.totalInProgress"
            color="red"
            floating
            rounded
            :label="
              auth.isAgent
                ? tradeStore.totalPending + tradeStore.totalInProgress
                : tradeStore.totalInProgress
            "
            class="q-mt-sm"
          />
        </q-btn>
        <template v-if="q.screen.gt.sm">
          <DropDownUser />
          <theme-toggle-bar />
          <i-18n-btn />
        </template>
        <q-btn
          v-else
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleSidebarMenu"
        />
      </div>
    </q-toolbar>
  </q-header>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore, useThemeStore, useTradeStore } from 'src/stores';
import DropDownAsset from './DropDownAsset.vue';
import DropDownUser from './DropDownUser.vue';
import I18nBtn from 'src/components/I18nBtn.vue';
import ThemeToggleBar from 'src/components/ThemeToggleBar.vue';
import PvLogoFull from 'src/assets/images/logo-full.png';
import PvLogoMb from 'src/assets/images/logo.png';

const q = useQuasar();
const route = useRoute();
const auth = useAuthStore();
const tradeStore = useTradeStore();
defineProps<{
  toggleSidebarMenu: () => void;
  privateNfyOpen: () => void;
}>();

const themes = useThemeStore();
const isDev = import.meta.env.DEV;
</script>

<style scoped>
.header {
  background-color: transparent;
  backdrop-filter: blur(12px);
}

.toolbar {
  height: 3.5rem;
}

.desktop-logo {
  width: 137px;
  height: 2.5rem;
}

.mobile-logo {
  width: 1.875rem;
  height: 2.5rem;
}
</style>
