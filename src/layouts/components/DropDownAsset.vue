<template>
  <q-icon
    :name="walletsStore.showBalance ? 'visibility' : 'visibility_off'"
    color="white"
    size="20px"
    @click="
      () => {
        walletsStore.toggleShowBalance();
      }
    "
  />

  <q-btn flat>
    <q-skeleton
      v-if="loadingWallets"
      type="text"
      style="width: 8rem; height: 2rem"
    />
    <div v-else-if="walletsStore.showBalance">
      <span class="text-subtitle2 text-bold q-px-sm q-mt-xs">{{
        DEFAULT_CRYPTO
      }}</span>
      <span class="text-h6 text-bold">
        {{ thousandTool(walletsStore.totalAmount, DEFAULT_CRYPTO) }}</span
      >
    </div>
    <div v-else class="blur-white">
      <span class="text-subtitle2 text-bold q-px-sm q-mt-xs">{{
        DEFAULT_CRYPTO
      }}</span>
      <span class="text-h6 text-bold">
        {{ thousandTool(walletsStore.totalAmount, DEFAULT_CRYPTO) }}</span
      >
    </div>

    <q-menu
      :offset="[0, 8]"
      anchor="bottom middle"
      self="top middle"
      class="no-shadow column flex-center"
      style="
        background-color: rgba(82, 9, 185, 0.4);
        backdrop-filter: blur(12px);
        width: 20rem;
        height: 22rem;
        border-radius: 20px;
      "
    >
      <AssetOverview />
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { useAuthStore, useTradeStore, useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
import { thousandTool } from 'src/utils/NumberTool';
import { useWallets } from 'src/pages/wallets/api';
import AssetOverview from 'src/components/AssetOverview.vue';
import { TradeStatusEnum } from 'src/utils/enums';

const auth = useAuthStore();
const tradeStore = useTradeStore();
const walletsStore = useWalletsStore();
const { data: wallets, loading: loadingWallets, refresh } = useWallets();

watch(wallets, (newWallets) => {
  if (newWallets && newWallets.length > 0) {
    walletsStore.setWallets(newWallets);
  }
});
watch(
  () => walletsStore.update,
  () => {
    refresh();
  }
);
watch(
  () => tradeStore.trade,
  (newTrade) => {
    if (!newTrade) return;

    const userId = auth.loginRes?.userId;
    if (
      (newTrade.userId === userId || newTrade.agentId === userId) &&
      [
        TradeStatusEnum.Pending,
        TradeStatusEnum.Accepted,
        TradeStatusEnum.Completed,
        TradeStatusEnum.Cancelled,
        TradeStatusEnum.Expired,
      ].includes(newTrade.status)
    ) {
      refresh();
    }
  }
);
</script>
