<template>
  <q-btn flat round icon="account_circle">
    <q-menu :offset="[0, 8]" anchor="bottom middle" self="top middle">
      <q-list>
        <q-item class="q-py-md">
          <q-item-section avatar>
            <q-avatar>
              <img :src="userLogo" />
            </q-avatar>
          </q-item-section>

          <q-item-section v-if="showInfo">
            <q-item-label>{{ auth.loginRes?.email }}</q-item-label>
            <q-item-label>{{ auth.loginRes?.phoneNumber }}</q-item-label>
          </q-item-section>
          <q-item-section v-else class="blur-black">
            <q-item-label>{{ auth.loginRes?.email }}</q-item-label>
            <q-item-label>{{ auth.loginRes?.phoneNumber }}</q-item-label>
          </q-item-section>

          <q-item-section avatar>
            <q-icon
              :name="showInfo ? 'visibility' : 'visibility_off'"
              size="sm"
              @click="
                () => {
                  showInfo = !showInfo;
                }
              "
            />
          </q-item-section>
        </q-item>

        <q-separator />

        <q-item clickable v-close-popup>
          <q-item-section
            @click="
              () => {
                router.push({ path: 'change-password' });
              }
            "
          >
            {{ t('dropDownUser.changePassword') }}
          </q-item-section>
        </q-item>
        <!-- <q-item clickable v-close-popup>
          <q-item-section>{{ t('dropDownUser.setup2FA') }}</q-item-section>
        </q-item> -->

        <q-separator />

        <q-item clickable v-close-popup>
          <q-item-section @click="logout">
            {{ t('dropDownUser.logout') }}
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import userLogo from 'src/assets/images/avatar.png';
import { useLogout } from 'src/api';
import { reactive, ref } from 'vue';
import { useAuthStore } from 'src/stores';

const { t } = useI18n();
const router = useRouter();
const auth = useAuthStore();
const { run: logout } = useLogout(
  reactive({
    config: {
      onSuccess: () => {
        router.replace({ name: 'login' });
      },
    },
  })
);

const showInfo = ref(false);
</script>
