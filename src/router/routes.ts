import { RouteRecordRaw } from 'vue-router';

declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth?: boolean;
  }
}
export const devEnv = import.meta.env.DEV;
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/auth',
  },
  {
    path: '/external',
    component: () => import('src/layouts/ExternalLayout.vue'),
    children: [
      {
        path: ':entryCode?',
        name: 'external-payment-detail',
        component: () =>
          import('src/pages/external/ExternalPaymentDetailPageRefactored.vue'),
      },
    ],
  },
  {
    path: '/main',
    component: () => import('src/layouts/PrivateLayout.vue'),
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        name: 'main',
        redirect: '/main/home',
      },
      {
        path: 'home',
        name: 'home',
        component: () => import('src/pages/home/<USER>'),
      },
      {
        path: 'trade',
        name: 'trade',
        component: () => import('src/pages/trade/TradePage.vue'),
      },
      {
        path: 'transfer',
        name: 'transfer',
        component: () => import('src/pages/transfer/TransferPage.vue'),
      },
      {
        path: 'wallets',
        name: 'wallets',
        component: () => import('src/pages/wallets/WalletsPage.vue'),
      },
      {
        path: 'history',
        name: 'history',
        component: () => import('src/pages/history/HistoryPage.vue'),
      },
      {
        path: 'accounts',
        name: 'accounts',
        component: () => import('pages/accounts/AccountsPage.vue'),
      },
      {
        path: 'change-password',
        name: 'change-password',
        component: () => import('pages/auth/changePw/ChangePage.vue'),
      },
    ],
  },
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('pages/auth/LoginPage.vue'),
      },
      // {
      //   path: 'register',
      //   name: 'register',
      //   component: () => import('pages/auth/register/RegisterPage.vue'),
      // },
      // {
      //   path: 'change',
      //   name: 'change',
      //   component: () => import('pages/auth/change/ChangePage.vue'),
      // },
      {
        path: '/auth',
        redirect: '/auth/login',
      },
    ],
  },
  {
    name: 'not-found',
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
