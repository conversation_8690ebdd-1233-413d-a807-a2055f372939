import { route } from 'quasar/wrappers';
import { LoginRes } from 'src/pages/auth/api/useLogin';
import { forage } from 'src/utils/foragePkg';
import {
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  Router,
} from 'vue-router';

import routes from './routes';

let routerInstance: Router;

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  routerInstance = Router;

  Router.beforeEach(async (to, _, next) => {
    const loginRes = await forage<LoginRes>().getItem('loginRes');

    if (to.meta.requiresAuth && !loginRes) {
      next({ name: 'login' });
    } else if (to.name === 'login' && loginRes) {
      next({ name: 'home' });
    } else {
      next();
    }
  });

  return Router;
});

export { routerInstance as router };
