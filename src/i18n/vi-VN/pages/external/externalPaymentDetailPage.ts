export default {
  waitUntilWithCountdown: 'Ch<PERSON> đến {datetime}, còn {seconds} giây.',
  title: 'Chi Tiết Thanh Toán',
  currentPrice: 'Gi<PERSON> Hiện Tại',
  orderTime: 'Thời Gian Đặt Hàng',
  paymentAmount: 'S<PERSON> Tiề<PERSON>',
  orderNumber: 'Số Đơn Hà<PERSON>',
  recipientBank: 'Ngân Hàng <PERSON>ườ<PERSON>ậ<PERSON>',
  recipientAccount: 'S<PERSON> T<PERSON>',
  recipientName: 'Tên <PERSON>ờ<PERSON>',
  buyerName: 'Tên <PERSON>ờ<PERSON>',
  payerName: 'Tên <PERSON>',
  paymentMethod: 'Phương Thức <PERSON>',
  paymentNotice: 'Thông Báo <PERSON>h <PERSON>án',
  sellPaymentNotice: 'Thông Báo Đơn Bán',
  paymentInstructions:
    'Vui lòng hoàn tất thanh toán trong vòng 15 phút, nếu không đơn hàng sẽ tự động bị hủy.',
  sellPaymentInstructions:
    '<PERSON><PERSON> lòng chờ người mua hoàn tất thanh toán. Bạn sẽ nhận được thông báo khi thanh toán hoàn tất.',
  sellOrderPending: 'Lệnh Bán Đang Chờ',
  sellWaitingForAgentPayment: 'Chờ Đại Lý Thanh Toán',
  sellConfirmReceipt: 'Xác Nhận Đã Nhận Thanh Toán',
  sellPaymentConfirmed: 'Đã Xác Nhận Nhận Thanh Toán',
  sellOrderCompleted: 'Lệnh Bán Hoàn Thành',
  sellOrderCancelled: 'Lệnh Bán Đã Hủy',
  sellOrderExpired: 'Lệnh Bán Đã Hết Hạn',

  // Sell Order Messages
  sellOrderPendingMessage:
    'Lệnh bán của bạn đang được xử lý. Vui lòng chờ đại lý xác nhận.',
  sellWaitingForAgentPaymentMessage:
    'Đại lý đang chuẩn bị thanh toán cho bạn. Bạn sẽ được thông báo khi thanh toán được gửi.',
  sellConfirmReceiptMessage:
    'Đại lý đã gửi thanh toán. Vui lòng kiểm tra tài khoản và xác nhận đã nhận.',
  sellPaymentConfirmedMessage:
    'Bạn đã xác nhận nhận thanh toán. Giao dịch đang được hoàn tất.',
  sellOrderCompletedMessage:
    'Lệnh bán của bạn đã hoàn thành thành công. Cảm ơn bạn đã sử dụng dịch vụ!',
  sellOrderCancelledMessage:
    'Lệnh bán của bạn đã bị hủy. Nếu có thắc mắc, vui lòng liên hệ hỗ trợ.',
  sellOrderExpiredMessage:
    'Lệnh bán của bạn đã hết hạn. Vui lòng tạo lệnh mới nếu muốn tiếp tục.',

  // Buy Order Titles
  buyOrderPending: 'Lệnh Mua Đang Chờ',
  buyOrderAccepted: 'Lệnh Mua Đã Chấp Nhận',
  buyMakePayment: 'Thực Hiện Thanh Toán',
  buyWaitingForConfirmation: 'Chờ Xác Nhận',
  buyPaymentConfirmed: 'Thanh Toán Đã Xác Nhận',
  buyOrderCompleted: 'Lệnh Mua Hoàn Thành',
  buyOrderCancelled: 'Lệnh Mua Đã Hủy',
  buyOrderExpired: 'Lệnh Mua Đã Hết Hạn',

  // Buy Order Messages
  buyOrderPendingMessage:
    'Lệnh mua của bạn đang được xử lý. Vui lòng chờ đại lý chấp nhận.',
  buyOrderAcceptedMessage:
    'Lệnh của bạn đã được chấp nhận. Thông tin tài khoản ngân hàng sẽ được cung cấp sớm.',
  buyMakePaymentMessage:
    'Vui lòng thanh toán vào tài khoản ngân hàng được cung cấp và tải lên bằng chứng thanh toán.',
  buyWaitingForConfirmationMessage:
    'Đã nhận thanh toán. Đại lý đang xác nhận thanh toán của bạn.',
  buyPaymentConfirmedMessage:
    'Thanh toán của bạn đã được xác nhận. Giao dịch đang được xử lý.',
  buyOrderCompletedMessage:
    'Lệnh mua của bạn đã hoàn thành thành công. Cảm ơn bạn đã sử dụng dịch vụ!',
  buyOrderCancelledMessage:
    'Lệnh mua của bạn đã bị hủy. Nếu có thắc mắc, vui lòng liên hệ hỗ trợ.',
  buyOrderExpiredMessage:
    'Lệnh mua của bạn đã hết hạn. Vui lòng tạo lệnh mới nếu muốn tiếp tục.',
  confirmPaymentButton: 'Tôi đã hoàn tất thanh toán',
  confirmPaymentReceivedButton: 'Tôi đã nhận được thanh toán',
  waitingForAgentPayment: 'Đang Chờ Nhân Viên Thanh Toán',
  confirmed: 'Đã Xác Nhận',
  cancelButton: 'Hủy Đơn Hàng',
  customerService: 'Chat với nhân viên',
  online: 'Đang trực tuyến',
  typeMessage: 'Gõ tin nhắn...',
  welcomeMessage: 'Xin chào! Chào mừng bạn đến với dịch vụ khách hàng K28U.',
  helpMessage: 'Tôi có thể giúp gì cho bạn về đơn hàng hôm nay?',
  autoReply:
    'Cảm ơn bạn đã gửi tin nhắn. Bộ phận chăm sóc khách hàng sẽ phản hồi trong giây lát.',
  imageReceived: 'Cảm ơn bạn đã gửi hình ảnh. Chúng tôi sẽ kiểm tra sớm.',
  invalidFileType: 'Vui lòng chọn file hình ảnh (JPG, PNG, GIF).',
  fileTooLarge: 'Kích thước ảnh quá lớn. Tối đa 5MB.',
  imageLoadError: 'Không tải được hình ảnh. Vui lòng thử lại.',
  imageUploadError: 'Không mở được bộ chọn file. Vui lòng thử lại.',
  connectionError: 'Lỗi kết nối. Vui lòng thử lại.',
  messageSendError: 'Gửi tin nhắn thất bại. Vui lòng thử lại.',
  verifying: 'Đang xác minh mã truy cập của bạn...',
  verificationError:
    'Xác minh mã truy cập thất bại. Vui lòng tạo đơn hàng mới để lấy mã truy cập mới.',
  tryAgain: 'Thử lại',
  copiedOrderNumber: 'Đã sao chép số đơn hàng vào bộ nhớ tạm',
  copiedAccountNumber: 'Đã sao chép số tài khoản vào bộ nhớ tạm',
  copiedRecipientBankName:
    'Đã sao chép tên ngân hàng người nhận vào bộ nhớ tạm',
  copiedRecipientName: 'Đã sao chép tên người nhận vào bộ nhớ tạm',
  copyFailed: 'Sao chép vào bộ nhớ tạm thất bại',
  paymentConfirmed:
    'Đã xác nhận thanh toán. Chúng tôi đang xử lý đơn hàng của bạn.',
  paymentError: 'Lỗi khi xác nhận thanh toán. Vui lòng thử lại.',
  orderCompleted: 'Đơn hàng của bạn đã hoàn tất thành công!',
  cancelConfirmTitle: 'Hủy Đơn Hàng',
  cancelConfirmMessage:
    'Bạn có chắc chắn muốn hủy đơn hàng này? Hành động này không thể hoàn tác.',
  timeRemaining: 'Thời Gian Còn Lại',
  orderExpired: 'Đơn hàng đã hết hạn. Vui lòng tạo đơn hàng mới.',
  cryptoAmount: 'Số Lượng Crypto',
  cryptoAmountPaid: 'Số Lượng Crypto Đã Thanh Toán',
  cryptoAmountHaveToBePaid: 'Số Lượng Crypto Cần Thanh Toán',
  cryptoAmountWillReceive: 'Số Lượng Crypto Sẽ Nhận Được',
  cryptoAmountReceived: 'Số Lượng Crypto Đã Nhận',
  fiatAmountPaid: 'Số Tiền Đã Thanh Toán',
  fiatAmountReceived: 'Số Tiền Đã Nhận',
  fiatAmountHaveToBePaid: 'Số Tiền Cần Thanh Toán',
  fiatAmountWillReceive: 'Số Tiền Sẽ Nhận Được',
  agentFee: 'Phí Đại Lý',
  orderStatus: 'Trạng Thái Đơn Hàng',
  network: 'Mạng',
  tradeType: 'Loại Giao Dịch',
  uploadPaymentProofButton: 'Tải lên Biên Lai Thanh Toán',
  viewPaymentProofButton: 'Xem Biên Lai Thanh Toán',
  paymentProofDialogTitle: 'Biên Lai Thanh Toán',
  paymentProofUploaded: 'Tải biên lai thanh toán thành công',
  paymentProofUploadError:
    'Tải biên lai thanh toán thất bại. Vui lòng thử lại.',
  closeButton: 'Đóng',
  confirmPaymentDialogTitle: 'Xác Nhận Thanh Toán',
  confirmPaymentDialogMessage:
    'Bạn có chắc chắn muốn xác nhận thanh toán? Hành động này không thể hoàn tác.',
  confirmButton: 'Có',
  cancelButtonn: 'Hủy',
  confirmFundsCheckbox:
    'Tôi xác nhận tiền đã có trong tài khoản, tên người thanh toán và số tiền là chính xác.',
  paymentDetailsTitle: 'Chi Tiết Thanh Toán',
  paymentDetailsBankName: 'Tên Ngân Hàng',
  paymentDetailsAccountName: 'Tên Tài Khoản',
  paymentDetailsAccountNumber: 'Số Tài Khoản',
  paymentDetailsBranchCode: 'Mã Chi Nhánh',
  yourAccountNumber: 'Số Tài Khoản Của Bạn',
  yourBankName: 'Tên Ngân Hàng Của Bạn',
  yourAccountName: 'Tên Tài Khoản Của Bạn',
  yourBranchCode: 'Mã Chi Nhánh Của Bạn',
  copiedPaymentAccountNumber:
    'Đã sao chép số tài khoản thanh toán vào bộ nhớ tạm',
  waitingForAgentMessage: 'Đang chờ nhân viên gửi chi tiết thanh toán...',
  agentWillSendDetailsSoon: 'Nhân viên sẽ gửi chi tiết sớm',
  transactionCompleted: 'Giao Dịch Đã Hoàn Thành',
  thankYouForPayment: 'Cảm ơn bạn đã thanh toán!',
  transactionNo: 'Số Giao Dịch',
  completedAt: 'Hoàn Thành Lúc',
  amountPaid: 'Số Tiền Đã Thanh Toán',
  cryptoReceived: 'Số Crypto Đã Nhận',
  exchangeRate: 'Tỷ Giá',
  recipientInfo: 'Thông Tin Người Nhận',
  payerInfo: 'Thông Tin Người Trả',
  payeeInfo: 'Thông Tin Người Nhận',
  needHelp:
    'Cần hỗ trợ? Liên hệ bộ phận hỗ trợ nếu bạn có thắc mắc về giao dịch này.',
  downloadReceipt: 'Tải Biên Lai',
  transactionDetails: 'Chi Tiết Giao Dịch',
  viewTransactionHistory: 'Xem Lịch Sử Giao Dịch',
  backToDashboard: 'Quay Lại Bảng Điều Khiển',
  yourCrypto: 'Crypto Của Bạn',
  paymentInfo: 'Thông Tin Thanh Toán',
  bankTransferDetails: 'Chi Tiết Chuyển Khoản Ngân Hàng',
  receiverDetails: 'Chi Tiết Người Nhận',
  printReceipt: 'In Biên Lai',
  saveAsPdf: 'Lưu dưới dạng PDF',
  shareReceipt: 'Chia sẻ Biên Lai',
  transactionSummary: 'Tóm Tắt Giao Dịch',
  helpAndSupport: 'Hỗ Trợ & Giúp Đỡ',
  faqTitle: 'Câu Hỏi Thường Gặp',
  supportCenter: 'Trung Tâm Hỗ Trợ',
  chatWithUs: 'Chat với chúng tôi',
  contactPhone: 'Số Điện Thoại Liên Hệ',
  howItWorksTitle: 'Cách Thức Hoạt Động',
  securityGuarantee: 'Bảo Đảm An Toàn',
  newOrder: 'Đơn Hàng Mới',
  viewBlockchainExplorer: 'Xem trên Blockchain Explorer',
  orderDetails: 'Chi Tiết Đơn Hàng',
  paymentDetailsSubtitle:
    'Thông tin ngân hàng bên dưới là của người bán. Vui lòng chuyển tiền vào tài khoản này.',
  sellPaymentDetailsSubtitle: 'Thông tin ngân hàng của bạn',
  copyText: 'Sao chép vào bộ nhớ tạm',
  copyRecipientAccount: 'Sao chép số tài khoản người nhận',
  copyPayerBankAccountName: 'Sao chép tên tài khoản ngân hàng người trả',
  copyAccountNumber: 'Sao chép số tài khoản',
  copyBranchCode: 'Sao chép mã chi nhánh',
  copyRecipientBankName: 'Sao chép tên ngân hàng người nhận',
  copyRecipientName: 'Sao chép tên người nhận',
  orderOverview: 'Tổng Quan Đơn Hàng',
  paymentStatus: 'Trạng Thái Thanh Toán',
  processingPayment: 'Đang Xử Lý Thanh Toán',
  awaitingPayment: 'Đang Chờ Thanh Toán',
  verifyingPayment: 'Đang Xác Minh Thanh Toán',
  transferDetails: 'Chi Tiết Chuyển Khoản',
  accountInformation: 'Thông Tin Tài Khoản',
  uploaded: 'Đã Tải Lên',
  notesTitle: 'Lưu ý:',
  note1:
    'Nền tảng này chỉ hỗ trợ giao dịch USDT, số lượng thực tế có thể không chính xác hoàn toàn.',
  note2:
    'Nền tảng không thể xác minh danh tính người gửi, vui lòng đảm bảo thông tin chính xác.',
  note3:
    'Nền tảng chỉ xác minh được liệu tiền đã chuyển vào tài khoản chỉ định hay chưa.',
  note4:
    'Vui lòng đảm bảo bạn có thể sử dụng USDT để thanh toán trước khi đặt hàng.',
  note5:
    'Số lượng mua tối thiểu: 100 USDT, các giao dịch nhỏ có thể không được xử lý do tắc nghẽn mạng.',
  note6:
    'Vui lòng tránh các trang web lừa đảo và liên kết độc hại để tránh bị đánh cắp tài khoản.',
  note7:
    'Nếu bạn có bất kỳ thắc mắc nào về đơn hàng, vui lòng liên hệ bộ phận chăm sóc khách hàng qua trang web.',
  waitingForConfirmation: 'Đang chờ bên kia xác nhận',
  waitingForConfirmationMessage:
    'Khi bên kia xác nhận đã nhận được, hệ thống sẽ tự động chuyển tiền điện tử vào tài khoản của bạn.',
  orderCancelled: 'Đơn hàng đã bị hủy',
  orderCancelledMessage: 'Đơn hàng này đã bị hủy.',
  orderCancelledTitle: 'Đơn hàng đã bị hủy',
  orderCancelledDescription:
    'Đơn hàng này đã bị hủy. Nếu bạn có thắc mắc, vui lòng liên hệ bộ phận chăm sóc khách hàng.',
  orderCompletedMessage: 'Đơn hàng của bạn đã hoàn thành thành công!',
  orderExpiredMessage: 'Đơn hàng đã hết hạn. Vui lòng tạo đơn hàng mới.',
  agentNotAcceptingOrderError:
    'Đại lý hiện không chấp nhận đơn hàng. Vui lòng tạo đơn hàng mới!',
  OrderErrorOrderId: 'Số đơn hàng:',
  orderExpiredError: 'Đơn hàng đã hết hạn. Vui lòng tạo đơn hàng mới.',
  copiedFiatAmount: 'Số tiền pháp định đã được sao chép vào clipboard',
  imagePreview: 'Xem trước hình ảnh',
  payerBankAccountName: 'Tên tài khoản ngân hàng người trả',
  orderCancelledSubtitle: 'Giao dịch này đã bị hủy',
  whatHappened: 'Chuyện gì đã xảy ra?',
  orderCancelledExplanation:
    'Đơn hàng này đã bị hủy do hết thời gian hoặc lý do khác. Tiền của bạn vẫn an toàn.',
  needAssistance: 'Cần hỗ trợ?',
  contactSupport: 'Liên hệ đội ngũ hỗ trợ nếu bạn có bất kỳ câu hỏi nào',
  processing: 'Đang xử lý...',
  processingConfirmation: 'Đang chờ xác nhận...',
  waitingForPaymentReceipt: 'Đang chờ xác nhận thanh toán',
  waitingForPaymentReceiptMessage:
    'Vui lòng chờ người mua hoàn tất thanh toán. Bạn sẽ nhận được thông báo khi thanh toán hoàn tất.',
  confirmPaymentReceivedDialogTitle: 'Xác nhận đã nhận thanh toán',
  confirmPaymentReceivedDialogMessage:
    'Bạn có chắc chắn đã nhận được thanh toán? Hành động này không thể hoàn tác.',
  paymentReceivedConfirmed: 'Xác nhận nhận thanh toán thành công',
  disputeButton: 'Khiếu nại',
  disputeDialogTitle: 'Gửi khiếu nại',
  disputeDialogMessage:
    'Bạn có chắc chắn muốn gửi khiếu nại cho đơn hàng này? Đội ngũ hỗ trợ sẽ xem xét vụ việc.',
  submitDisputeButton: 'Gửi khiếu nại',
  disputeSubmitted:
    'Khiếu nại đã được gửi thành công. Đội ngũ hỗ trợ sẽ liên hệ với bạn sớm.',
  disputeError: 'Gửi khiếu nại thất bại. Vui lòng thử lại.',
};
