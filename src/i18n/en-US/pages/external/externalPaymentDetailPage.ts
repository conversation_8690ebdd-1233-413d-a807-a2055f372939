export default {
  waitUntilWithCountdown: 'Wait until {datetime}, {seconds} seconds left.',
  title: 'Payment Details',
  currentPrice: 'Current Price',
  orderTime: 'Order Time',
  paymentAmount: 'Payment Amount',
  fiatAmountReceived: 'Amount Received',
  fiatAmountPaid: 'Amount Paid',
  fiatAmountHaveToBePaid: 'Amount Have to be Paid',
  fiatAmountWillReceive: 'Amount Will Receive',
  orderNumber: 'Order Number',
  recipientBank: 'Recipient Bank',
  recipientAccount: 'Account Number',
  recipientName: 'Recipient Name',
  payerName: 'Payer Name',
  buyerName: 'Buyer Name',
  paymentMethod: 'Payment Method',
  paymentNotice: 'Payment Notice',
  sellPaymentNotice: 'Sell Order Notice',
  paymentInstructions:
    'Please complete the payment within 15 minutes, otherwise the order will be automatically cancelled.',

  sellOrderPending: 'Sell Order Pending',
  sellWaitingForAgentPayment: 'Waiting for Agent Payment',
  sellConfirmReceipt: 'Confirm Payment Receipt',
  sellPaymentConfirmed: 'Payment Receipt Confirmed',
  sellOrderCompleted: 'Sell Order Completed',
  sellOrderCancelled: 'Sell Order Cancelled',
  sellOrderExpired: 'Sell Order Expired',

  // Sell Order Messages
  sellOrderPendingMessage:
    'Your sell order is being processed. Please wait for agent confirmation.',
  sellWaitingForAgentPaymentMessage:
    'Agent is preparing your payment. You will be notified once payment is sent.',
  sellConfirmReceiptMessage:
    'Agent has sent the payment. Please check your account and confirm receipt.',
  sellPaymentConfirmedMessage:
    'You have confirmed receipt of payment. Transaction is being finalized.',
  sellOrderCompletedMessage:
    'Your sell order has been completed successfully. Thank you for your business!',
  sellOrderCancelledMessage:
    'Your sell order has been cancelled. If you have any questions, please contact support.',
  sellOrderExpiredMessage:
    'Your sell order has expired. Please create a new order if you wish to continue.',
  sellPaymentInstructions:
    'Please wait for the buyer to complete the payment. You will receive a notification once payment is made.',
  // Buy Order Titles
  buyOrderPending: 'Buy Order Pending',
  buyOrderAccepted: 'Buy Order Accepted',
  buyMakePayment: 'Make Payment',
  buyWaitingForConfirmation: 'Waiting for Confirmation',
  buyPaymentConfirmed: 'Payment Confirmed',
  buyOrderCompleted: 'Buy Order Completed',
  buyOrderCancelled: 'Buy Order Cancelled',
  buyOrderExpired: 'Buy Order Expired',

  // Buy Order Messages
  buyOrderPendingMessage:
    'Your buy order is being processed. Please wait for agent acceptance.',
  buyOrderAcceptedMessage:
    'Your order has been accepted. Bank account details will be provided shortly.',
  buyMakePaymentMessage:
    'Please make payment to the provided bank account and upload proof of payment.',
  buyWaitingForConfirmationMessage:
    'Payment received. Agent is confirming your payment.',
  buyPaymentConfirmedMessage:
    'Your payment has been confirmed. Transaction is being processed.',
  buyOrderCompletedMessage:
    'Your buy order has been completed successfully. Thank you for your business!',
  buyOrderCancelledMessage:
    'Your buy order has been cancelled. If you have any questions, please contact support.',
  buyOrderExpiredMessage:
    'Your buy order has expired. Please create a new order if you wish to continue.',

  confirmPaymentButton: 'I have completed the payment',
  confirmPaymentReceivedButton: 'I have received the payment',
  waitingForAgentPayment: 'Waiting for Agent Payment',
  confirmed: 'Confirmed',
  cancelButton: 'Cancel Order',
  customerService: 'Chat with agent',
  online: 'Online',
  typeMessage: 'Type a message...',
  welcomeMessage: 'Hello! Welcome to K28U customer service.',
  helpMessage: 'How can I help you with your order today?',
  autoReply:
    'Thank you for your message. Our customer service team will respond shortly.',
  imageReceived: 'Thank you for sending the image. We will review it shortly.',
  invalidFileType: 'Please select an image file (JPG, PNG, GIF).',
  fileTooLarge: 'Image file is too large. Maximum size is 5MB.',
  imageLoadError: 'Failed to load the image. Please try again.',
  imageUploadError: 'Failed to open file selector. Please try again.',
  connectionError: 'Connection error. Please try again.',
  messageSendError: 'Failed to send message. Please try again.',
  verifying: 'Verifying your entry code...',
  verificationError:
    'Failed to verify entry code. Please recreate the order to obtain a new entry code.',
  agentNotAcceptingOrderError:
    'Agent is not accepting orders at the moment. Please create a new order!',
  OrderErrorOrderId: 'OrderID:',
  orderExpiredError: 'Order expired. Please create a new order.',
  tryAgain: 'Try Again',
  copiedOrderNumber: 'Order number copied to clipboard',
  copiedAccountNumber: 'Account number copied to clipboard',
  copiedRecipientBankName: 'Recipient bank name copied to clipboard',
  copiedRecipientName: 'Recipient name copied to clipboard',
  copiedPayerBankAccountName: 'Payer bank account name copied to clipboard',
  copiedFiatAmount: 'Fiat amount copied to clipboard',
  copyFailed: 'Failed to copy to clipboard',
  paymentConfirmed: 'Payment confirmed. We are processing your order.',
  paymentError: 'Error confirming payment. Please try again.',
  orderCompleted: 'Your order has been completed successfully!',
  cancelConfirmTitle: 'Cancel Order',
  cancelConfirmMessage:
    'Are you sure you want to cancel this order? This action cannot be undone.',
  timeRemaining: 'Time Remaining',
  orderExpired: 'Order expired. Please create a new order.',
  cryptoAmount: 'Crypto Amount',
  cryptoAmountReceived: 'Crypto Amount Received',
  cryptoAmountPaid: 'Crypto Amount Paid',
  cryptoAmountHaveToBePaid: 'Crypto Amount Have to be Paid',
  cryptoAmountWillReceive: 'Crypto Amount Will Receive',
  agentFee: 'Agent Fee',
  orderStatus: 'Order Status',
  network: 'Network',
  tradeType: 'Trade Type',
  uploadPaymentProofButton: 'Upload Payment Proof',
  viewPaymentProofButton: 'View Payment Proof',
  paymentProofDialogTitle: 'Payment Proof',
  paymentProofUploaded: 'Payment proof uploaded successfully',
  paymentProofUploadError: 'Failed to upload payment proof. Please try again.',
  closeButton: 'Close',
  confirmPaymentDialogTitle: 'Confirm Payment',
  confirmPaymentReceivedDialogTitle: 'Confirm Payment Received',
  confirmPaymentDialogMessage:
    'Are you sure you want to confirm the payment? This action cannot be undone.',
  confirmPaymentReceivedDialogMessage:
    'Are you sure you have received the payment? This action cannot be undone.',
  confirmButton: 'Yes',
  cancelButtonn: 'Cancel',
  confirmFundsCheckbox:
    'I confirm the funds are in my account, and the Payer’s Name and Amount are correct.',
  paymentDetailsTitle: 'Payment Details',
  paymentDetailsBankName: 'Bank Name',
  paymentDetailsAccountName: 'Account Name',
  paymentDetailsAccountNumber: 'Account Number',
  paymentDetailsBranchCode: 'Branch Code',
  yourAccountNumber: 'Your Account Number',
  yourBankName: 'Your Bank Name',
  yourAccountName: 'Your Account Name',
  yourBranchCode: 'Your Branch Code',
  copiedPaymentAccountNumber: 'Payment account number copied to clipboard',
  waitingForAgentMessage: 'Waiting for agent to send payment details...',
  agentWillSendDetailsSoon: 'Agent will send details soon',
  transactionCompleted: 'Transaction Completed',
  thankYouForPayment: 'Thank you for your payment!',
  transactionNo: 'Transaction No',
  completedAt: 'Completed At',
  amountPaid: 'Amount Paid',
  cryptoReceived: 'Crypto Received',
  exchangeRate: 'Exchange Rate',
  recipientInfo: 'Recipient Info',
  payerInfo: 'Payer Info',
  payeeInfo: 'Payee Info',
  buyerInfo: 'Buyer Info',
  needHelp:
    'Need help? Contact our support if you have questions about this transaction.',
  downloadReceipt: 'Download Receipt',
  transactionDetails: 'Transaction Details',
  viewTransactionHistory: 'View Transaction History',
  backToDashboard: 'Back to Dashboard',
  yourCrypto: 'Your Crypto',
  paymentInfo: 'Payment Information',
  bankTransferDetails: 'Bank Transfer Details',
  receiverDetails: 'Receiver Details',
  printReceipt: 'Print Receipt',
  saveAsPdf: 'Save as PDF',
  shareReceipt: 'Share Receipt',
  transactionSummary: 'Transaction Summary',
  helpAndSupport: 'Help & Support',
  faqTitle: 'Frequently Asked Questions',
  supportCenter: 'Support Center',
  chatWithUs: 'Chat with Us',
  contactPhone: 'Contact Phone',
  howItWorksTitle: 'How It Works',
  securityGuarantee: 'Security Guarantee',
  newOrder: 'New Order',
  viewBlockchainExplorer: 'View on Blockchain Explorer',
  orderDetails: 'Order Details',
  paymentDetailsSubtitle:
    'Agent bank account details. Please make payment to this account.',
  sellPaymentDetailsSubtitle: 'Your bank account details',
  copyText: 'Copy to clipboard',
  copyRecipientAccount: 'Copy recipient account',
  copyPayerBankAccountName: 'Copy payer bank account name',
  copyAccountNumber: 'Copy account number',
  copyBranchCode: 'Copy branch code',
  copyRecipientBankName: 'Copy recipient bank name',
  copyRecipientName: 'Copy recipient name',
  orderOverview: 'Order Overview',
  paymentStatus: 'Payment Status',
  processingPayment: 'Processing Payment',
  awaitingPayment: 'Awaiting Payment',
  verifyingPayment: 'Verifying Payment',
  transferDetails: 'Transfer Details',
  accountInformation: 'Account Information',
  uploaded: 'Uploaded',
  notesTitle: 'Notes:',
  note1:
    'This platform only supports USDT transactions, and the actual number may not be exactly the same.',
  note2:
    'This platform cannot verify the identity of the sender, please ensure the accuracy of the information.',
  note3:
    'This platform can only verify whether the funds have arrived in the designated account.',
  note4:
    'Please confirm that you can use USDT for payment before placing an order.',
  note5:
    'Minimum purchase amount: 100USDT, small amounts may not be processed due to network congestion.',
  note6:
    'Please avoid phishing websites and malicious links to prevent account theft.',
  note7:
    'If you have any questions about the order, please contact our customer service through the website.',
  waitingForConfirmation: 'Waiting for the other party to confirm',
  waitingForPaymentReceipt: 'Waiting for Payment Receipt',
  waitingForConfirmationMessage:
    'Once the other party confirms receipt, the system will automatically transfer the cryptocurrency to your account.',
  waitingForPaymentReceiptMessage:
    'Please wait for the buyer to complete the payment. You will be notified once payment is received.',
  orderCancelled: 'Order Cancelled',
  orderCancelledMessage: 'This order has been cancelled.',
  orderCancelledTitle: 'Order Cancelled',
  orderCancelledDescription:
    'This order has been cancelled. If you have any questions, please contact customer service.',
  orderCompletedMessage: 'Your order has been completed successfully!',
  orderExpiredMessage: 'This order has expired. Please create a new order.',
  imagePreview: 'Image Preview',
  payerBankAccountName: 'Payer Bank Account Name',
  orderCancelledSubtitle: 'This transaction has been cancelled',
  whatHappened: 'What happened?',
  orderCancelledExplanation:
    'This order was cancelled due to timeout or other reasons. Your funds are safe.',
  needAssistance: 'Need Assistance?',
  contactSupport: 'Contact our support team if you have any questions',
  processing: 'Processing...',
  processingConfirmation: 'Waiting for confirmation...',
  paymentReceivedConfirmed: 'Payment receipt confirmed successfully',
  disputeButton: 'Dispute',
  disputeDialogTitle: 'Submit Dispute',
  disputeDialogMessage:
    'Are you sure you want to submit a dispute for this order? Our support team will review the case.',
  submitDisputeButton: 'Submit Dispute',
  disputeSubmitted:
    'Dispute submitted successfully. Our support team will contact you soon.',
  disputeError: 'Failed to submit dispute. Please try again.',
};
