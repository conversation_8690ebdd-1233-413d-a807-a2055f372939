import { useAuthStore, useTradeStore } from 'src/stores';
import { forage } from 'src/utils/foragePkg';
import { Reactive } from 'vue';
import { Options, useRequest } from 'vue-request';

export const logout = () => {
  const response = new Promise<boolean>((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, 1000);
  });
  return response;
};

export const cleanupSession = (callback?: () => void) => {
  const authStore = useAuthStore();
  const tradeStore = useTradeStore();
  tradeStore.reset();
  authStore.reset();
  forage().clear(callback);
};

export type LogoutProps = {
  config?: Options<boolean, [object]>;
};
export const useLogout = (reactiveProps?: Reactive<LogoutProps>) => {
  const vueRequest = useRequest(logout, {
    manual: true,
    ...reactiveProps?.config,
    ready: () => true,
    onSuccess: async (res, params) => {
      const loginProps = await forage<string>().getItem('loginProps');
      const rememberMe = await forage<boolean>().getItem('rememberMe');

      cleanupSession();
      if (reactiveProps?.config?.onSuccess)
        reactiveProps.config.onSuccess(res, params);

      forage().setItem('loginProps', loginProps);
      forage().setItem('rememberMe', rememberMe);
    },
  });

  return { logout, ...vueRequest };
};
