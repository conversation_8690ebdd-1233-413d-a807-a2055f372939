import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

interface CurrencyCode {
  code: string;
  name: string;
  countryCode: string;
  emojiFlag: string;
}

type CurrencyCodesRes = Array<CurrencyCode>;

const useCurrencyCodes = () => {
  const vueRequest = requestProvider<CurrencyCodesRes>(
    () => {
      const request = axiosProvider
        .get('/common/currency-codes')
        .then(({ data }) => data);

      return request;
    },
    {
      manual: false,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useCurrencyCodes };
export type { CurrencyCode };
